<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <base href="/" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://dev.vn.euroland.com/tools/common/integration.js" ></script>
    <link rel="stylesheet" href="./src/index.css">
    <script src="/euroland-app-context-widget.umd.js"></script>
    <script src="/auth-widget.umd.js"></script>
    <title>Vite + Preact + TS</title>
  </head>
  <body>
    <euroland-auth></euroland-auth>
      <button onclick="window.EurolandAppContext.command('login')()">Login</button>
    <euroland-watch-list></euroland-watch-list>
    <euroland-add-instrument instrumentId="32864"></euroland-add-instrument>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
