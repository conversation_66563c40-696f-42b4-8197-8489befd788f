!function(){"use strict";try{if("undefined"!=typeof document){var e=document.createElement("style");e.appendChild(document.createTextNode(".tab-bar{border-bottom:1px solid #e1e3e6;padding:0;overflow-x:auto;overflow-y:hidden}.tabs-container{display:flex;align-items:flex-end;gap:2px;min-width:min-content}.tab{display:flex;align-items:center;min-width:120px;max-width:240px;height:30px;background:#e9ecef;border:1px solid #d1d4dc;border-bottom:none;border-radius:4px 4px 0 0;cursor:pointer;transition:all .2s;position:relative;padding:0 8px;margin-bottom:1px}.tab:hover{background:#dee2e6}.tab.active{background:#fff;border-color:#e1e3e6;margin-bottom:0;z-index:1}.tab-content{display:flex;align-items:center;gap:4px;flex:1;min-width:0}.tab-name{font-size:14px;font-weight:500;color:#131722;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1}.tab-star{color:#ffc107;flex-shrink:0}.tab-count{font-size:12px;color:#6c757d;flex-shrink:0}.tab-actions{display:flex;align-items:center;gap:2px;opacity:0;transition:opacity .2s;margin-left:16px}.tab:hover .tab-actions,.tab.active .tab-actions{opacity:1}.tab-action-btn{display:flex;align-items:center;justify-content:center;border:none;border-radius:3px;background:transparent;color:#6c757d;cursor:pointer;transition:all .2s}button.tab-action-btn{width:20px;height:20px;padding:0}.tab-action-btn:hover{background:#0000001a;color:#131722}.close-tab-btn:hover{background:#f443361a;color:#f44336}.tab-action-btn:disabled{opacity:.3;cursor:not-allowed}.tab-action-btn:disabled:hover{background:transparent;color:#6c757d}.tab-edit-form{flex:1;padding:2px}.tab-edit-input{width:100%;padding:4px 6px;border:1px solid #2962ff;border-radius:3px;background:#fff;color:#131722;font-size:14px;font-weight:500}.tab-edit-input:focus{outline:none;border-color:#2962ff}.delete-confirm{display:flex;align-items:center;gap:2px}.confirm-delete-btn{display:flex;align-items:center;justify-content:center;border-radius:3px;border:1px solid #f44336;color:#f44336;cursor:pointer;transition:background-color .2s}button.confirm-delete-btn{width:20px;height:20px;padding:0}.cancel-delete-btn{display:flex;align-items:center;justify-content:center;border-radius:3px;border:1px solid #6c757d;color:#6c757d;cursor:pointer;transition:background-color .2s}button.cancel-delete-btn{width:20px;height:20px;padding:0}.add-tab-btn{display:flex;align-items:center;justify-content:center;background:#e9ecef;border:1px solid #d1d4dc;border-bottom:none;border-radius:4px 4px 0 0;cursor:pointer;transition:all .2s;color:#6c757d;margin-bottom:1px;flex-shrink:0}button.add-tab-btn{width:30px;height:30px;padding:0}.add-tab-btn:hover{background:#dee2e6;color:#131722}.add-tab-form{display:flex;align-items:center;gap:4px;min-width:200px;height:30px;background:#fff;border:1px solid #2962ff;border-bottom:none;border-radius:4px 4px 0 0;padding:0 8px;margin-bottom:1px;flex-shrink:0}.add-tab-input{flex:1;padding:4px 6px;border:none;background:transparent;color:#131722;font-size:14px;font-weight:500;min-width:100px}.add-tab-input:focus{outline:none}.add-tab-input::placeholder{color:#6c757d}.add-tab-actions{display:flex;align-items:center;gap:2px;flex-shrink:0}.confirm-add-btn{display:flex;align-items:center;justify-content:center;border:1px solid #00c853;border-radius:3px;color:#00c853;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.confirm-add-btn{width:20px;height:20px;padding:0}.cancel-add-btn{display:flex;align-items:center;justify-content:center;border:1px solid #6c757d;border-radius:3px;color:#6c757d;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.cancel-add-btn{width:20px;height:20px;padding:0}@media (max-width: 768px){.tab{min-width:120px;max-width:180px}}@media (max-width: 480px){.tab{min-width:120px;max-width:120px;padding:0 4px}.tab-name{font-size:12px}.tab-count{display:none}}.add-instrument-section{position:relative;width:100%;padding:8px 0;border-top:1px solid #e1e3e6}.add-section-title{margin:0 0 8px;font-size:16px;font-weight:600;color:#131722}.search-container{position:relative}.search-icon{position:absolute;left:4px;top:50%;transform:translateY(-50%);color:#6c757d}.search-input{width:100%;padding:10px 8px 10px 24px;border:1px solid #d1d4dc;border-radius:6px;background:#fff;color:#131722;font-size:14px}.search-input:focus{outline:none;border-color:#2962ff}.search-results{position:absolute;top:84px;left:0;right:0;border:1px solid #e1e3e6;border-radius:4%;background:#fff;margin-bottom:16px;z-index:999;box-shadow:0 2px 4px #0000001a}.search-results-empty{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:24px;color:var(--text-secondary);text-align:center;min-height:120px}.search-results-empty-icon{font-size:24px;margin-bottom:8px;color:#d1d4dc}.search-results-empty-text{font-size:14px;color:#6c757d}.search-results .rc-virtual-list,.search-results .rc-virtual-list-holder,.search-results .rc-virtual-list-holder-inner{border-radius:6px}.search-result-item{display:flex;align-items:center;justify-content:space-between;cursor:pointer;padding:12px 16px;gap:4px;border-bottom:1px solid #f1f3f4;transition:background-color .2s;height:60px;box-sizing:border-box}.search-result-item:hover{background:#f8f9fa}.search-result-item:last-child{border-bottom:none}.instrument-info{display:flex;align-items:center;gap:16px;flex:1}.instrument-info .symbol{font-weight:600;color:#131722;min-width:60px}.instrument-info .name{color:#6c757d;flex:1}.instrument-info .abbreviation{font-weight:600;color:#131722;min-width:80px}.instrument-info .change{font-weight:500;min-width:100px}.instrument-info .change.positive{color:#00c853}.instrument-info .change.negative{color:#f44336}.add-instrument-btn{display:flex;align-items:center;justify-content:center;color:#a4a6ac;border:none;border-radius:4px;padding-left:16px;cursor:pointer;transition:background-color .2s}button.add-instrument-btn{width:36px;height:36px;padding:0}button.add-instrument-btn:hover{background:#d8dbe2;color:#fff}button.add-instrument-btn:disabled{background:#d1d4dc;cursor:not-allowed}@media (max-width: 768px){.instrument-info{flex-direction:column;align-items:flex-start;gap:4px}.instrument-info .symbol,.instrument-info .price,.instrument-info .change{min-width:auto}}.instruments-section{flex:1;padding:10px 0 0;overflow-y:auto;max-height:calc(100vh - 430px);display:flex;flex-direction:column}.instruments-table-container{width:100%;overflow-y:auto;max-height:calc(100vh - 430px);border:1px solid #e1e3e6;border-radius:6px}.instruments-table{width:100%;border-collapse:collapse;background:#fff;table-layout:fixed}.instruments-table thead{position:sticky;top:0;z-index:1}.table-header{background:#f8f9fa;padding:8px 12px;font-weight:600;font-size:12px;text-transform:uppercase;letter-spacing:.5px;color:#6c757d;border-bottom:1px solid #e1e3e6;text-align:left}.table-row{display:table-row;position:relative;cursor:pointer;border-bottom:1px solid #f1f3f4;transition:background-color .2s}.table-row td{display:table-cell;padding:8px 12px;vertical-align:middle}.header-cell{display:flex;align-items:center}.symbol{flex-direction:column;align-items:flex-start}.symbol-text{font-weight:600}.market-text{font-size:11px;color:#6c757d}.positive{color:#28a745}.negative{color:#dc3545}.remove-btn{background:none;border:none;cursor:pointer;color:#dc3545;padding:0}.table-row:hover .remove-btn{visibility:visible}.table-row.selected{background:#e3f2fd}.table-row:last-child{border-bottom:none}.cell{display:flex;align-items:center;font-size:14px;font-weight:500}.cell.symbol{display:flex;flex-direction:column;align-items:flex-start;gap:2px}.symbol-text{font-weight:600;color:#131722}.market-text{font-size:11px;color:#6c757d;font-weight:400}.cell.name{color:#6c757d;font-weight:400}.cell.price{color:#131722;font-weight:600}.cell.change{font-weight:500}.cell.change-percent{text-align:end;font-weight:500}.cell.change.positive{color:#00c853}.cell.change.negative{color:#f44336}.cell.change-percent.positive{color:#00c853}.cell.change-percent.negative{color:#f44336}.cell.high,.cell.low,.cell.week-high{color:#131722;font-weight:600}.remove-btn{background:none;visibility:hidden;position:absolute;right:0;top:50%;transform:translateY(-50%);background:#fff;border:none;cursor:pointer;padding:6px;border-radius:4px;color:#6c757d;transition:all .2s}.remove-btn:hover{background:#f3938c;color:#f44336}.delete-confirm{display:flex;gap:4px}.confirm-delete-btn,.cancel-delete-btn{background:none;border:none;cursor:pointer;padding:6px;border-radius:4px;display:flex;align-items:center;justify-content:center;transition:all .2s}.confirm-delete-btn{color:#00c853}.confirm-delete-btn:hover{background:#00c8531a}.cancel-delete-btn{color:#6c757d}.cancel-delete-btn:hover{background:#6c757d1a}.empty-state,.loading-state,.error-state{display:flex;flex-direction:column;justify-content:center;align-items:center;height:200px;text-align:center;color:#6c757d}.empty-state p,.loading-state p,.error-state p{margin:4px 0;font-size:14px}.error-state{color:#f44336}")),document.head.appendChild(e)}}catch(t){console.error("vite-plugin-css-injected-by-js",t)}}();
!function(t){"function"==typeof define&&define.amd?define(t):t()}(function(){"use strict";var t,e,n,r,i,o,a,s,c,l,u,d,f,h={},p=[],m=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,v=Array.isArray;function y(t,e){for(var n in e)t[n]=e[n];return t}function g(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function b(e,n,r){var i,o,a,s={};for(a in n)"key"==a?i=n[a]:"ref"==a?o=n[a]:s[a]=n[a];if(arguments.length>2&&(s.children=arguments.length>3?t.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===s[a]&&(s[a]=e.defaultProps[a]);return _(e,s,i,o,null)}function _(t,r,i,o,a){var s={type:t,props:r,key:i,ref:o,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==a?++n:a,__i:-1,__u:0};return null==a&&null!=e.vnode&&e.vnode(s),s}function w(t){return t.children}function x(t,e){this.props=t,this.context=e}function k(t,e){if(null==e)return t.__?k(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?k(t):null}function S(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return S(t)}}function O(t){(!t.__d&&(t.__d=!0)&&i.push(t)&&!E.__r++||o!=e.debounceRendering)&&((o=e.debounceRendering)||a)(E)}function E(){for(var t,n,r,o,a,c,l,u=1;i.length;)i.length>u&&i.sort(s),t=i.shift(),u=i.length,t.__d&&(r=void 0,a=(o=(n=t).__v).__e,c=[],l=[],n.__P&&((r=y({},o)).__v=o.__v+1,e.vnode&&e.vnode(r),F(n.__P,r,o,n.__n,n.__P.namespaceURI,32&o.__u?[a]:null,c,null==a?k(o):a,!!(32&o.__u),l),r.__v=o.__v,r.__.__k[r.__i]=r,j(c,r,l),r.__e!=a&&S(r)));E.__r=0}function C(t,e,n,r,i,o,a,s,c,l,u){var d,f,m,y,g,b,x=r&&r.__k||p,S=e.length;for(c=function(t,e,n,r,i){var o,a,s,c,l,u=n.length,d=u,f=0;for(t.__k=new Array(i),o=0;o<i;o++)null!=(a=e[o])&&"boolean"!=typeof a&&"function"!=typeof a?(c=o+f,(a=t.__k[o]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?_(null,a,null,null,null):v(a)?_(w,{children:a},null,null,null):null==a.constructor&&a.__b>0?_(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=t,a.__b=t.__b+1,s=null,-1!=(l=a.__i=A(a,n,c,d))&&(d--,(s=n[l])&&(s.__u|=2)),null==s||null==s.__v?(-1==l&&(i>u?f--:i<u&&f++),"function"!=typeof a.type&&(a.__u|=4)):l!=c&&(l==c-1?f--:l==c+1?f++:(l>c?f--:f++,a.__u|=4))):t.__k[o]=null;if(d)for(o=0;o<u;o++)null!=(s=n[o])&&!(2&s.__u)&&(s.__e==r&&(r=k(s)),q(s,s));return r}(n,e,x,c,S),d=0;d<S;d++)null!=(m=n.__k[d])&&(f=-1==m.__i?h:x[m.__i]||h,m.__i=d,b=F(t,m,f,i,o,a,s,c,l,u),y=m.__e,m.ref&&f.ref!=m.ref&&(f.ref&&I(f.ref,null,m),u.push(m.ref,m.__c||y,m)),null==g&&null!=y&&(g=y),4&m.__u||f.__k===m.__k?c=T(m,c,t):"function"==typeof m.type&&void 0!==b?c=b:y&&(c=y.nextSibling),m.__u&=-7);return n.__e=g,c}function T(t,e,n){var r,i;if("function"==typeof t.type){for(r=t.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=t,e=T(r[i],e,n));return e}t.__e!=e&&(e&&t.type&&!n.contains(e)&&(e=k(t)),n.insertBefore(t.__e,e||null),e=t.__e);do{e=e&&e.nextSibling}while(null!=e&&8==e.nodeType);return e}function R(t,e){return e=e||[],null==t||"boolean"==typeof t||(v(t)?t.some(function(t){R(t,e)}):e.push(t)),e}function A(t,e,n,r){var i,o,a=t.key,s=t.type,c=e[n];if(null===c&&null==t.key||c&&a==c.key&&s==c.type&&!(2&c.__u))return n;if(r>(null==c||2&c.__u?0:1))for(i=n-1,o=n+1;i>=0||o<e.length;){if(i>=0){if((c=e[i])&&!(2&c.__u)&&a==c.key&&s==c.type)return i;i--}if(o<e.length){if((c=e[o])&&!(2&c.__u)&&a==c.key&&s==c.type)return o;o++}}return-1}function N(t,e,n){"-"==e[0]?t.setProperty(e,null==n?"":n):t[e]=null==n?"":"number"!=typeof n||m.test(e)?n:n+"px"}function P(t,e,n,r,i){var o,a;t:if("style"==e)if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||N(t.style,e,"");if(n)for(e in n)r&&n[e]==r[e]||N(t.style,e,n[e])}else if("o"==e[0]&&"n"==e[1])o=e!=(e=e.replace(c,"$1")),a=e.toLowerCase(),e=a in t||"onFocusOut"==e||"onFocusIn"==e?a.slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=n,n?r?n.u=r.u:(n.u=l,t.addEventListener(e,o?d:u,o)):t.removeEventListener(e,o?d:u,o);else{if("http://www.w3.org/2000/svg"==i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=e&&"height"!=e&&"href"!=e&&"list"!=e&&"form"!=e&&"tabIndex"!=e&&"download"!=e&&"rowSpan"!=e&&"colSpan"!=e&&"role"!=e&&"popover"!=e&&e in t)try{t[e]=null==n?"":n;break t}catch(s){}"function"==typeof n||(null==n||!1===n&&"-"!=e[4]?t.removeAttribute(e):t.setAttribute(e,"popover"==e&&1==n?"":n))}}function M(t){return function(n){if(this.l){var r=this.l[n.type+t];if(null==n.t)n.t=l++;else if(n.t<r.u)return;return r(e.event?e.event(n):n)}}}function F(n,r,i,o,a,s,c,l,u,d){var f,p,m,b,_,S,O,E,T,R,A,N,M,F,j,I,q,U=r.type;if(null!=r.constructor)return null;128&i.__u&&(u=!!(32&i.__u),s=[l=r.__e=i.__e]),(f=e.__b)&&f(r);t:if("function"==typeof U)try{if(E=r.props,T="prototype"in U&&U.prototype.render,R=(f=U.contextType)&&o[f.__c],A=f?R?R.props.value:f.__:o,i.__c?O=(p=r.__c=i.__c).__=p.__E:(T?r.__c=p=new U(E,A):(r.__c=p=new x(E,A),p.constructor=U,p.render=L),R&&R.sub(p),p.props=E,p.state||(p.state={}),p.context=A,p.__n=o,m=p.__d=!0,p.__h=[],p._sb=[]),T&&null==p.__s&&(p.__s=p.state),T&&null!=U.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=y({},p.__s)),y(p.__s,U.getDerivedStateFromProps(E,p.__s))),b=p.props,_=p.state,p.__v=r,m)T&&null==U.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),T&&null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(T&&null==U.getDerivedStateFromProps&&E!==b&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(E,A),!p.__e&&null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(E,p.__s,A)||r.__v==i.__v){for(r.__v!=i.__v&&(p.props=E,p.state=p.__s,p.__d=!1),r.__e=i.__e,r.__k=i.__k,r.__k.some(function(t){t&&(t.__=r)}),N=0;N<p._sb.length;N++)p.__h.push(p._sb[N]);p._sb=[],p.__h.length&&c.push(p);break t}null!=p.componentWillUpdate&&p.componentWillUpdate(E,p.__s,A),T&&null!=p.componentDidUpdate&&p.__h.push(function(){p.componentDidUpdate(b,_,S)})}if(p.context=A,p.props=E,p.__P=n,p.__e=!1,M=e.__r,F=0,T){for(p.state=p.__s,p.__d=!1,M&&M(r),f=p.render(p.props,p.state,p.context),j=0;j<p._sb.length;j++)p.__h.push(p._sb[j]);p._sb=[]}else do{p.__d=!1,M&&M(r),f=p.render(p.props,p.state,p.context),p.state=p.__s}while(p.__d&&++F<25);p.state=p.__s,null!=p.getChildContext&&(o=y(y({},o),p.getChildContext())),T&&!m&&null!=p.getSnapshotBeforeUpdate&&(S=p.getSnapshotBeforeUpdate(b,_)),I=f,null!=f&&f.type===w&&null==f.key&&(I=D(f.props.children)),l=C(n,v(I)?I:[I],r,i,o,a,s,c,l,u,d),p.base=r.__e,r.__u&=-161,p.__h.length&&c.push(p),O&&(p.__E=p.__=null)}catch(z){if(r.__v=null,u||null!=s)if(z.then){for(r.__u|=u?160:128;l&&8==l.nodeType&&l.nextSibling;)l=l.nextSibling;s[s.indexOf(l)]=null,r.__e=l}else for(q=s.length;q--;)g(s[q]);else r.__e=i.__e,r.__k=i.__k;e.__e(z,r,i)}else null==s&&r.__v==i.__v?(r.__k=i.__k,r.__e=i.__e):l=r.__e=function(n,r,i,o,a,s,c,l,u){var d,f,p,m,y,b,_,w=i.props,x=r.props,S=r.type;if("svg"==S?a="http://www.w3.org/2000/svg":"math"==S?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=s)for(d=0;d<s.length;d++)if((y=s[d])&&"setAttribute"in y==!!S&&(S?y.localName==S:3==y.nodeType)){n=y,s[d]=null;break}if(null==n){if(null==S)return document.createTextNode(x);n=document.createElementNS(a,S,x.is&&x),l&&(e.__m&&e.__m(r,s),l=!1),s=null}if(null==S)w===x||l&&n.data==x||(n.data=x);else{if(s=s&&t.call(n.childNodes),w=i.props||h,!l&&null!=s)for(w={},d=0;d<n.attributes.length;d++)w[(y=n.attributes[d]).name]=y.value;for(d in w)if(y=w[d],"children"==d);else if("dangerouslySetInnerHTML"==d)p=y;else if(!(d in x)){if("value"==d&&"defaultValue"in x||"checked"==d&&"defaultChecked"in x)continue;P(n,d,null,y,a)}for(d in x)y=x[d],"children"==d?m=y:"dangerouslySetInnerHTML"==d?f=y:"value"==d?b=y:"checked"==d?_=y:l&&"function"!=typeof y||w[d]===y||P(n,d,y,w[d],a);if(f)l||p&&(f.__html==p.__html||f.__html==n.innerHTML)||(n.innerHTML=f.__html),r.__k=[];else if(p&&(n.innerHTML=""),C("template"==r.type?n.content:n,v(m)?m:[m],r,i,o,"foreignObject"==S?"http://www.w3.org/1999/xhtml":a,s,c,s?s[0]:i.__k&&k(i,0),l,u),null!=s)for(d=s.length;d--;)g(s[d]);l||(d="value","progress"==S&&null==b?n.removeAttribute("value"):null!=b&&(b!==n[d]||"progress"==S&&!b||"option"==S&&b!=w[d])&&P(n,d,b,w[d],a),d="checked",null!=_&&_!=n[d]&&P(n,d,_,w[d],a))}return n}(i.__e,r,i,o,a,s,c,u,d);return(f=e.diffed)&&f(r),128&r.__u?void 0:l}function j(t,n,r){for(var i=0;i<r.length;i++)I(r[i],r[++i],r[++i]);e.__c&&e.__c(n,t),t.some(function(n){try{t=n.__h,n.__h=[],t.some(function(t){t.call(n)})}catch(r){e.__e(r,n.__v)}})}function D(t){return"object"!=typeof t||null==t||t.__b&&t.__b>0?t:v(t)?t.map(D):y({},t)}function I(t,n,r){try{if("function"==typeof t){var i="function"==typeof t.__u;i&&t.__u(),i&&null==n||(t.__u=t(n))}else t.current=n}catch(o){e.__e(o,r)}}function q(t,n,r){var i,o;if(e.unmount&&e.unmount(t),(i=t.ref)&&(i.current&&i.current!=t.__e||I(i,null,n)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(a){e.__e(a,n)}i.base=i.__P=null}if(i=t.__k)for(o=0;o<i.length;o++)i[o]&&q(i[o],n,r||"function"!=typeof t.type);r||g(t.__e),t.__c=t.__=t.__e=void 0}function L(t,e,n){return this.constructor(t,n)}function U(n,r,i){var o,a,s,c;r==document&&(r=document.documentElement),e.__&&e.__(n,r),a=(o="function"==typeof i)?null:i&&i.__k||r.__k,s=[],c=[],F(r,n=(!o&&i||r).__k=b(w,null,[n]),a||h,h,r.namespaceURI,!o&&i?[i]:a?null:r.firstChild?t.call(r.childNodes):null,s,!o&&i?i:a?a.__e:r.firstChild,o,c),j(s,n,c)}function z(t,e){U(t,e,z)}function B(e,n,r){var i,o,a,s,c=y({},e.props);for(a in e.type&&e.type.defaultProps&&(s=e.type.defaultProps),n)"key"==a?i=n[a]:"ref"==a?o=n[a]:c[a]=void 0===n[a]&&null!=s?s[a]:n[a];return arguments.length>2&&(c.children=arguments.length>3?t.call(arguments,2):r),_(e.type,c,i||e.key,o||e.ref,null)}function W(t){function e(t){var n,r;return this.getChildContext||(n=new Set,(r={})[e.__c]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(t){this.props.value!=t.value&&n.forEach(function(t){t.__e=!0,O(t)})},this.sub=function(t){n.add(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){n&&n.delete(t),e&&e.call(t)}}),t.children}return e.__c="__cC"+f++,e.__=t,e.Provider=e.__l=(e.Consumer=function(t,e){return t.children(e)}).contextType=e,e}t=p.slice,e={__e:function(t,e,n,r){for(var i,o,a;e=e.__;)if((i=e.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(t)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),a=i.__d),a)return i.__E=i}catch(s){t=s}throw t}},n=0,r=function(t){return null!=t&&null==t.constructor},x.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=y({},this.state),"function"==typeof t&&(t=t(y({},n),this.props)),t&&y(n,t),null!=t&&this.__v&&(e&&this._sb.push(e),O(this))},x.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),O(this))},x.prototype.render=w,i=[],a="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,s=function(t,e){return t.__v.__b-e.__v.__b},E.__r=0,c=/(PointerCapture)$|Capture$/i,l=0,u=M(!1),d=M(!0),f=0;var H=0;function $(t,n,r,i,o,a){n||(n={});var s,c,l=n;if("ref"in l)for(c in l={},n)"ref"==c?s=n[c]:l[c]=n[c];var u={type:t,props:l,key:r,ref:s,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--H,__i:-1,__u:0,__source:o,__self:a};if("function"==typeof t&&(s=t.defaultProps))for(c in s)void 0===l[c]&&(l[c]=s[c]);return e.vnode&&e.vnode(u),u}var Q,V,K="Name",G="Document",J="OperationDefinition",X="Field",Y="FragmentDefinition";class Z extends Error{constructor(t,e,n,r,i,o,a){super(t),this.name="GraphQLError",this.message=t,i&&(this.path=i),e&&(this.nodes=Array.isArray(e)?e:[e]),n&&(this.source=n),r&&(this.positions=r),o&&(this.originalError=o);var s=a;if(!s&&o){var c=o.extensions;c&&"object"==typeof c&&(s=c)}this.extensions=s||{}}toJSON(){return{...this,message:this.message}}toString(){return this.message}get[Symbol.toStringTag](){return"GraphQLError"}}function tt(t){return new Z(`Syntax Error: Unexpected token at ${V} in ${t}`)}function et(t){if(t.lastIndex=V,t.test(Q))return Q.slice(V,V=t.lastIndex)}var nt=/ +(?=[^\s])/y;function rt(t){for(var e=t.split("\n"),n="",r=0,i=0,o=e.length-1,a=0;a<e.length;a++)nt.lastIndex=0,nt.test(e[a])&&(a&&(!r||nt.lastIndex<r)&&(r=nt.lastIndex),i=i||a,o=a);for(var s=i;s<=o;s++)s!==i&&(n+="\n"),n+=e[s].slice(r).replace(/\\"""/g,'"""');return n}function it(){for(var t=0|Q.charCodeAt(V++);9===t||10===t||13===t||32===t||35===t||44===t||65279===t;t=0|Q.charCodeAt(V++))if(35===t)for(;10!==(t=Q.charCodeAt(V++))&&13!==t;);V--}function ot(){for(var t=V,e=0|Q.charCodeAt(V++);e>=48&&e<=57||e>=65&&e<=90||95===e||e>=97&&e<=122;e=0|Q.charCodeAt(V++));if(t===V-1)throw tt("Name");var n=Q.slice(t,--V);return it(),n}function at(){return{kind:"Name",value:ot()}}var st=/(?:"""|(?:[\s\S]*?[^\\])""")/y,ct=/(?:(?:\.\d+)?[eE][+-]?\d+|\.\d+)/y;function lt(t){var e;switch(Q.charCodeAt(V)){case 91:V++,it();for(var n=[];93!==Q.charCodeAt(V);)n.push(lt(t));return V++,it(),{kind:"ListValue",values:n};case 123:V++,it();for(var r=[];125!==Q.charCodeAt(V);){var i=at();if(58!==Q.charCodeAt(V++))throw tt("ObjectField");it(),r.push({kind:"ObjectField",name:i,value:lt(t)})}return V++,it(),{kind:"ObjectValue",fields:r};case 36:if(t)throw tt("Variable");return V++,{kind:"Variable",name:at()};case 34:if(34===Q.charCodeAt(V+1)&&34===Q.charCodeAt(V+2)){if(V+=3,null==(e=et(st)))throw tt("StringValue");return it(),{kind:"StringValue",value:rt(e.slice(0,-3)),block:!0}}var o,a=V;V++;var s=!1;for(o=0|Q.charCodeAt(V++);92===o&&(V++,s=!0)||10!==o&&13!==o&&34!==o&&o;o=0|Q.charCodeAt(V++));if(34!==o)throw tt("StringValue");return e=Q.slice(a,V),it(),{kind:"StringValue",value:s?JSON.parse(e):e.slice(1,-1),block:!1};case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:for(var c,l=V++;(c=0|Q.charCodeAt(V++))>=48&&c<=57;);var u=Q.slice(l,--V);if(46===(c=Q.charCodeAt(V))||69===c||101===c){if(null==(e=et(ct)))throw tt("FloatValue");return it(),{kind:"FloatValue",value:u+e}}return it(),{kind:"IntValue",value:u};case 110:if(117===Q.charCodeAt(V+1)&&108===Q.charCodeAt(V+2)&&108===Q.charCodeAt(V+3))return V+=4,it(),{kind:"NullValue"};break;case 116:if(114===Q.charCodeAt(V+1)&&117===Q.charCodeAt(V+2)&&101===Q.charCodeAt(V+3))return V+=4,it(),{kind:"BooleanValue",value:!0};break;case 102:if(97===Q.charCodeAt(V+1)&&108===Q.charCodeAt(V+2)&&115===Q.charCodeAt(V+3)&&101===Q.charCodeAt(V+4))return V+=5,it(),{kind:"BooleanValue",value:!1}}return{kind:"EnumValue",value:ot()}}function ut(t){if(40===Q.charCodeAt(V)){var e=[];V++,it();do{var n=at();if(58!==Q.charCodeAt(V++))throw tt("Argument");it(),e.push({kind:"Argument",name:n,value:lt(t)})}while(41!==Q.charCodeAt(V));return V++,it(),e}}function dt(t){if(64===Q.charCodeAt(V)){var e=[];do{V++,e.push({kind:"Directive",name:at(),arguments:ut(t)})}while(64===Q.charCodeAt(V));return e}}function ft(){for(var t=0;91===Q.charCodeAt(V);)t++,V++,it();var e={kind:"NamedType",name:at()};do{if(33===Q.charCodeAt(V)&&(V++,it(),e={kind:"NonNullType",type:e}),t){if(93!==Q.charCodeAt(V++))throw tt("NamedType");it(),e={kind:"ListType",type:e}}}while(t--);return e}function ht(){if(123!==Q.charCodeAt(V++))throw tt("SelectionSet");return it(),pt()}function pt(){var t=[];do{if(46===Q.charCodeAt(V)){if(46!==Q.charCodeAt(++V)||46!==Q.charCodeAt(++V))throw tt("SelectionSet");switch(V++,it(),Q.charCodeAt(V)){case 64:t.push({kind:"InlineFragment",typeCondition:void 0,directives:dt(!1),selectionSet:ht()});break;case 111:110===Q.charCodeAt(V+1)?(V+=2,it(),t.push({kind:"InlineFragment",typeCondition:{kind:"NamedType",name:at()},directives:dt(!1),selectionSet:ht()})):t.push({kind:"FragmentSpread",name:at(),directives:dt(!1)});break;case 123:V++,it(),t.push({kind:"InlineFragment",typeCondition:void 0,directives:void 0,selectionSet:pt()});break;default:t.push({kind:"FragmentSpread",name:at(),directives:dt(!1)})}}else{var e=at(),n=void 0;58===Q.charCodeAt(V)&&(V++,it(),n=e,e=at());var r=ut(!1),i=dt(!1),o=void 0;123===Q.charCodeAt(V)&&(V++,it(),o=pt()),t.push({kind:"Field",alias:n,name:e,arguments:r,directives:i,selectionSet:o})}}while(125!==Q.charCodeAt(V));return V++,it(),{kind:"SelectionSet",selections:t}}function mt(){if(it(),40===Q.charCodeAt(V)){var t=[];V++,it();do{if(36!==Q.charCodeAt(V++))throw tt("Variable");var e=at();if(58!==Q.charCodeAt(V++))throw tt("VariableDefinition");it();var n=ft(),r=void 0;61===Q.charCodeAt(V)&&(V++,it(),r=lt(!0)),it(),t.push({kind:"VariableDefinition",variable:{kind:"Variable",name:e},type:n,defaultValue:r,directives:dt(!0)})}while(41!==Q.charCodeAt(V));return V++,it(),t}}function vt(){var t=at();if(111!==Q.charCodeAt(V++)||110!==Q.charCodeAt(V++))throw tt("FragmentDefinition");return it(),{kind:"FragmentDefinition",name:t,typeCondition:{kind:"NamedType",name:at()},directives:dt(!1),selectionSet:ht()}}function yt(){var t=[];do{if(123===Q.charCodeAt(V))V++,it(),t.push({kind:"OperationDefinition",operation:"query",name:void 0,variableDefinitions:void 0,directives:void 0,selectionSet:pt()});else{var e=ot();switch(e){case"fragment":t.push(vt());break;case"query":case"mutation":case"subscription":var n,r=void 0;40!==(n=Q.charCodeAt(V))&&64!==n&&123!==n&&(r=at()),t.push({kind:"OperationDefinition",operation:e,name:r,variableDefinitions:mt(),directives:dt(!1),selectionSet:ht()});break;default:throw tt("Document")}}}while(V<Q.length);return t}function gt(t,e,n){for(var r="",i=0;i<t.length;i++)i&&(r+=e),r+=n(t[i]);return r}var bt="\n",_t={OperationDefinition(t){var e=t.operation;return t.name&&(e+=" "+t.name.value),t.variableDefinitions&&t.variableDefinitions.length&&(t.name||(e+=" "),e+="("+gt(t.variableDefinitions,", ",_t.VariableDefinition)+")"),t.directives&&t.directives.length&&(e+=" "+gt(t.directives," ",_t.Directive)),"query"!==e?e+" "+_t.SelectionSet(t.selectionSet):_t.SelectionSet(t.selectionSet)},VariableDefinition(t){var e=_t.Variable(t.variable)+": "+wt(t.type);return t.defaultValue&&(e+=" = "+wt(t.defaultValue)),t.directives&&t.directives.length&&(e+=" "+gt(t.directives," ",_t.Directive)),e},Field(t){var e=t.alias?t.alias.value+": "+t.name.value:t.name.value;if(t.arguments&&t.arguments.length){var n=gt(t.arguments,", ",_t.Argument);e.length+n.length+2>80?e+="("+(bt+="  ")+gt(t.arguments,bt,_t.Argument)+(bt=bt.slice(0,-2))+")":e+="("+n+")"}return t.directives&&t.directives.length&&(e+=" "+gt(t.directives," ",_t.Directive)),t.selectionSet&&t.selectionSet.selections.length&&(e+=" "+_t.SelectionSet(t.selectionSet)),e},StringValue:t=>t.block?function(t){return'"""\n'+t.replace(/"""/g,'\\"""')+'\n"""'}(t.value).replace(/\n/g,bt):function(t){return JSON.stringify(t)}(t.value),BooleanValue:t=>""+t.value,NullValue:t=>"null",IntValue:t=>t.value,FloatValue:t=>t.value,EnumValue:t=>t.value,Name:t=>t.value,Variable:t=>"$"+t.name.value,ListValue:t=>"["+gt(t.values,", ",wt)+"]",ObjectValue:t=>"{"+gt(t.fields,", ",_t.ObjectField)+"}",ObjectField:t=>t.name.value+": "+wt(t.value),Document:t=>t.definitions&&t.definitions.length?gt(t.definitions,"\n\n",wt):"",SelectionSet:t=>"{"+(bt+="  ")+gt(t.selections,bt,wt)+(bt=bt.slice(0,-2))+"}",Argument:t=>t.name.value+": "+wt(t.value),FragmentSpread(t){var e="..."+t.name.value;return t.directives&&t.directives.length&&(e+=" "+gt(t.directives," ",_t.Directive)),e},InlineFragment(t){var e="...";return t.typeCondition&&(e+=" on "+t.typeCondition.name.value),t.directives&&t.directives.length&&(e+=" "+gt(t.directives," ",_t.Directive)),e+" "+_t.SelectionSet(t.selectionSet)},FragmentDefinition(t){var e="fragment "+t.name.value;return e+=" on "+t.typeCondition.name.value,t.directives&&t.directives.length&&(e+=" "+gt(t.directives," ",_t.Directive)),e+" "+_t.SelectionSet(t.selectionSet)},Directive(t){var e="@"+t.name.value;return t.arguments&&t.arguments.length&&(e+="("+gt(t.arguments,", ",_t.Argument)+")"),e},NamedType:t=>t.name.value,ListType:t=>"["+wt(t.type)+"]",NonNullType:t=>wt(t.type)+"!"},wt=t=>_t[t.kind](t);var xt=()=>{},kt=xt;function St(t){return{tag:0,0:t}}function Ot(t){return{tag:1,0:t}}var Et=()=>"function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator",Ct=t=>t;function Tt(t){return e=>n=>{var r=kt;e(e=>{0===e?n(0):0===e.tag?(r=e[0],n(e)):t(e[0])?n(e):r(0)})}}function Rt(t){return e=>n=>e(e=>{0===e||0===e.tag?n(e):n(Ot(t(e[0])))})}function At(t){return e=>n=>{var r=[],i=kt,o=!1,a=!1;e(e=>{var s,c;a||(0===e?(a=!0,r.length||n(0)):0===e.tag?i=e[0]:(o=!1,s=t(e[0]),c=kt,s(t=>{if(0===t){if(r.length){var e=r.indexOf(c);e>-1&&(r=r.slice()).splice(e,1),r.length||(a?n(0):o||(o=!0,i(0)))}}else 0===t.tag?(r.push(c=t[0]),c(0)):r.length&&(n(t),c(0))}),o||(o=!0,i(0))))}),n(St(t=>{if(1===t){a||(a=!0,i(1));for(var e=0,n=r,s=r.length;e<s;e++)n[e](1);r.length=0}else{a||o?o=!1:(o=!0,i(0));for(var c=0,l=r,u=r.length;c<u;c++)l[c](0)}}))}}function Nt(t){return function(t){return At(Ct)(t)}(zt(t))}function Pt(t){return e=>n=>{var r=!1;e(e=>{if(r);else if(0===e)r=!0,n(0),t();else if(0===e.tag){var i=e[0];n(St(e=>{1===e?(r=!0,i(1),t()):i(e)}))}else n(e)})}}function Mt(t){return e=>n=>{var r=!1;e(e=>{if(r);else if(0===e)r=!0,n(0);else if(0===e.tag){var i=e[0];n(St(t=>{1===t&&(r=!0),i(t)}))}else t(e[0]),n(e)})}}function Ft(t){return e=>n=>e(e=>{0===e?n(0):0===e.tag?(n(e),t()):n(e)})}function jt(t){var e=[],n=kt,r=!1;return i=>{e.push(i),1===e.length&&t(t=>{if(0===t){for(var i=0,o=e,a=e.length;i<a;i++)o[i](0);e.length=0}else if(0===t.tag)n=t[0];else{r=!1;for(var s=0,c=e,l=e.length;s<l;s++)c[s](t)}}),i(St(t=>{if(1===t){var o=e.indexOf(i);o>-1&&(e=e.slice()).splice(o,1),e.length||n(1)}else r||(r=!0,n(0))}))}}function Dt(t){return e=>n=>{var r=kt,i=kt,o=!1,a=!1,s=!1,c=!1;e(e=>{var l;c||(0===e?(c=!0,s||n(0)):0===e.tag?r=e[0]:(s&&(i(1),i=kt),o?o=!1:(o=!0,r(0)),l=t(e[0]),s=!0,l(t=>{s&&(0===t?(s=!1,c?n(0):o||(o=!0,r(0))):0===t.tag?(a=!1,(i=t[0])(0)):(n(t),a?a=!1:i(0)))})))}),n(St(t=>{1===t?(c||(c=!0,r(1)),s&&(s=!1,i(1))):(c||o||(o=!0,r(0)),s&&!a&&(a=!0,i(0)))}))}}function It(t){return e=>n=>{var r=kt,i=!1,o=0;e(e=>{i||(0===e?(i=!0,n(0)):0===e.tag?r=e[0]:o++<t?(n(e),!i&&o>=t&&(i=!0,n(0),r(1))):n(e))}),n(St(e=>{1!==e||i?0===e&&!i&&o<t&&r(0):(i=!0,r(1))}))}}function qt(t){return e=>n=>{var r=kt,i=kt,o=!1;e(e=>{o||(0===e?(o=!0,i(1),n(0)):0===e.tag?(r=e[0],t(t=>{0===t||(0===t.tag?(i=t[0])(0):(o=!0,i(1),r(1),n(0)))})):n(e))}),n(St(t=>{1!==t||o?o||r(0):(o=!0,r(1),i(1))}))}}function Lt(t,e){return n=>r=>{var i=kt,o=!1;n(n=>{o||(0===n?(o=!0,r(0)):0===n.tag?(i=n[0],r(n)):t(n[0])?r(n):(o=!0,e&&r(n),r(0),i(1)))})}}function Ut(t){return e=>{var n,r=t[Et()]&&t[Et()]()||t,i=!1,o=!1,a=!1;e(St(async t=>{if(1===t)i=!0,r.return&&r.return();else if(o)a=!0;else{for(a=o=!0;a&&!i;)if((n=await r.next()).done)i=!0,r.return&&await r.return(),e(0);else try{a=!1,e(Ot(n.value))}catch(s){if(!r.throw)throw s;(i=!!(await r.throw(s)).done)&&e(0)}o=!1}}))}}var zt=function(t){return t[Symbol.asyncIterator]?Ut(t):e=>{var n,r=t[Symbol.iterator](),i=!1,o=!1,a=!1;e(St(t=>{if(1===t)i=!0,r.return&&r.return();else if(o)a=!0;else{for(a=o=!0;a&&!i;)if((n=r.next()).done)i=!0,r.return&&r.return(),e(0);else try{a=!1,e(Ot(n.value))}catch(s){if(!r.throw)throw s;(i=!!r.throw(s).done)&&e(0)}o=!1}}))}};function Bt(t){return e=>{var n=!1;e(St(r=>{1===r?n=!0:n||(n=!0,e(Ot(t)),e(0))}))}}function Wt(t){return e=>{var n=!1,r=t({next(t){n||e(Ot(t))},complete(){n||(n=!0,e(0))}});e(St(t=>{1!==t||n||(n=!0,r())}))}}function Ht(t){return e=>{var n=kt,r=!1;return e(e=>{0===e?r=!0:0===e.tag?(n=e[0])(0):r||(t(e[0]),n(0))}),{unsubscribe(){r||(r=!0,n(1))}}}}var $t=t=>t&&"string"==typeof t.message&&(t.extensions||"GraphQLError"===t.name)?t:"object"==typeof t&&"string"==typeof t.message?new Z(t.message,t.nodes,t.source,t.positions,t.path,t,t.extensions||{}):new Z(t);class Qt extends Error{constructor(t){var e=(t.graphQLErrors||[]).map($t),n=((t,e)=>{var n="";if(t)return`[Network] ${t.message}`;if(e)for(var r=0,i=e.length;r<i;r++)n&&(n+="\n"),n+=`[GraphQL] ${e[r].message}`;return n})(t.networkError,e);super(n),this.name="CombinedError",this.message=n,this.graphQLErrors=e,this.networkError=t.networkError,this.response=t.response}toString(){return this.message}}var Vt=(t,e)=>{for(var n=0|(e||5381),r=0,i=0|t.length;r<i;r++)n=(n<<5)+n+t.charCodeAt(r);return n},Kt=new Set,Gt=new WeakMap,Jt=(t,e)=>{if(null===t||Kt.has(t))return"null";if("object"!=typeof t)return JSON.stringify(t)||"";if(t.toJSON)return Jt(t.toJSON(),e);if(Array.isArray(t)){for(var n="[",r=0,i=t.length;r<i;r++)n.length>1&&(n+=","),n+=Jt(t[r],e)||"null";return n+"]"}if(!e&&(te!==Zt&&t instanceof te||ee!==Zt&&t instanceof ee))return"null";var o=Object.keys(t).sort();if(!o.length&&t.constructor&&Object.getPrototypeOf(t).constructor!==Object.prototype.constructor){var a=Gt.get(t)||Math.random().toString(36).slice(2);return Gt.set(t,a),Jt({__key:a},e)}Kt.add(t);for(var s="{",c=0,l=o.length;c<l;c++){var u=Jt(t[o[c]],e);u&&(s.length>1&&(s+=","),s+=Jt(o[c],e)+":"+u)}return Kt.delete(t),s+"}"},Xt=(t,e,n)=>{if(null==n||"object"!=typeof n||n.toJSON||Kt.has(n));else if(Array.isArray(n))for(var r=0,i=n.length;r<i;r++)Xt(t,`${e}.${r}`,n[r]);else if(n instanceof te||n instanceof ee)t.set(e,n);else for(var o in Kt.add(n),n)Xt(t,`${e}.${o}`,n[o])},Yt=(t,e)=>(Kt.clear(),Jt(t,e||!1));class Zt{}var te="undefined"!=typeof File?File:Zt,ee="undefined"!=typeof Blob?Blob:Zt,ne=/("{3}[\s\S]*"{3}|"(?:\\.|[^"])*")/g,re=/(?:#[^\n\r]+)?(?:[\r\n]+|$)/g,ie=(t,e)=>e%2==0?t.replace(re,"\n"):t,oe=t=>t.split(ne).map(ie).join("").trim(),ae=new Map,se=new Map,ce=t=>{var e;return"string"==typeof t?e=oe(t):t.loc&&se.get(t.__key)===t?e=t.loc.source.body:(e=ae.get(t)||oe(function(t){return bt="\n",_t[t.kind]?_t[t.kind](t):""}(t)),ae.set(t,e)),"string"==typeof t||t.loc||(t.loc={start:0,end:e.length,source:{body:e,name:"gql",locationOffset:{line:1,column:1}}}),e},le=t=>{var e;if(t.documentId)e=Vt(t.documentId);else if(e=Vt(ce(t)),t.definitions){var n=fe(t);n&&(e=Vt(`\n# ${n}`,e))}return e},ue=t=>{var e,n;return"string"==typeof t?(e=le(t),n=se.get(e)||function(t,e){return Q=t.body?t.body:t,V=0,it(),e&&e.noLocation?{kind:"Document",definitions:yt()}:{kind:"Document",definitions:yt(),loc:{start:0,end:Q.length,startToken:void 0,endToken:void 0,source:{body:Q,name:"graphql.web",locationOffset:{line:1,column:1}}}}}(t,{noLocation:!0})):(e=t.__key||le(t),n=se.get(e)||t),n.loc||ce(n),n.__key=e,se.set(e,n),n},de=(t,e,n)=>{var r=e||{},i=ue(t),o=Yt(r,!0),a=i.__key;return"{}"!==o&&(a=Vt(o,a)),{key:a,query:i,variables:r,extensions:n}},fe=t=>{for(var e=0,n=t.definitions.length;e<n;e++){var r=t.definitions[e];if(r.kind===J)return r.name?r.name.value:void 0}},he=(t,e,n)=>{if(!("data"in e||"errors"in e&&Array.isArray(e.errors)))throw new Error("No Content");var r="subscription"===t.kind;return{operation:t,data:e.data,error:Array.isArray(e.errors)?new Qt({graphQLErrors:e.errors,response:n}):void 0,extensions:e.extensions?{...e.extensions}:void 0,hasNext:null==e.hasNext?r:e.hasNext,stale:!1}},pe=(t,e)=>{if("object"==typeof t&&null!=t){if(Array.isArray(t)){t=[...t];for(var n=0,r=e.length;n<r;n++)t[n]=pe(t[n],e[n]);return t}if(!t.constructor||t.constructor===Object){for(var i in t={...t},e)t[i]=pe(t[i],e[i]);return t}}return e},me=(t,e,n,r)=>{var i=t.error?t.error.graphQLErrors:[],o=!!t.extensions||!!(e.payload||e).extensions,a={...t.extensions,...(e.payload||e).extensions},s=e.incremental;"path"in e&&(s=[e]);var c={data:t.data};if(s)for(var l=function(){var t=s[u];Array.isArray(t.errors)&&i.push(...t.errors),t.extensions&&(Object.assign(a,t.extensions),o=!0);var e="data",n=c,l=[];if(t.path)l=t.path;else if(r){var d=r.find(e=>e.id===t.id);l=t.subPath?[...d.path,...t.subPath]:d.path}for(var f=0,h=l.length;f<h;e=l[f++])n=n[e]=Array.isArray(n[e])?[...n[e]]:{...n[e]};if(t.items)for(var p=+e>=0?e:0,m=0,v=t.items.length;m<v;m++)n[p+m]=pe(n[p+m],t.items[m]);else void 0!==t.data&&(n[e]=pe(n[e],t.data))},u=0,d=s.length;u<d;u++)l();else c.data=(e.payload||e).data||t.data,i=e.errors||e.payload&&e.payload.errors||i;return{operation:t.operation,data:c.data,error:i.length?new Qt({graphQLErrors:i,response:n}):void 0,extensions:o?a:void 0,hasNext:null!=e.hasNext?e.hasNext:t.hasNext,stale:!1}};var ve=t=>{var e=t.indexOf("?");return e>-1?[t.slice(0,e),new URLSearchParams(t.slice(e+1))]:[t,new URLSearchParams]},ye=(t,e)=>{var n,r={accept:"subscription"===t.kind?"text/event-stream, multipart/mixed":"application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed"},i=("function"==typeof t.context.fetchOptions?t.context.fetchOptions():t.context.fetchOptions)||{};if(i.headers)if("has"in(n=i.headers)&&!Object.keys(n).length)i.headers.forEach((t,e)=>{r[e]=t});else if(Array.isArray(i.headers))i.headers.forEach((t,e)=>{Array.isArray(t)?r[t[0]]?r[t[0]]=`${r[t[0]]},${t[1]}`:r[t[0]]=t[1]:r[e]=t});else for(var o in i.headers)r[o.toLowerCase()]=i.headers[o];var a=((t,e)=>{if(e&&("query"!==t.kind||!t.context.preferGetMethod)){var n=Yt(e),r=(s=e.variables,c=new Map,te===Zt&&ee===Zt||(Kt.clear(),Xt(c,"variables",s)),c);if(r.size){var i=new FormData;i.append("operations",n),i.append("map",Yt({...[...r.keys()].map(t=>[t])}));var o=0;for(var a of r.values())i.append(""+o++,a);return i}return n}var s,c})(t,e);return"string"!=typeof a||r["content-type"]||(r["content-type"]="application/json"),{...i,method:a?"POST":"GET",body:a,headers:r}},ge=/boundary="?([^=";]+)"?/i,be=/data: ?([^\n]+)/;async function*_e(t){if(t.body[Symbol.asyncIterator])for await(var e of t.body)yield e;else{var n,r=t.body.getReader();try{for(;!(n=await r.read()).done;)yield n.value}finally{r.cancel()}}}async function*we(t,e){var n,r="undefined"!=typeof TextDecoder?new TextDecoder:null,i="";for await(var o of t)for(i+="Buffer"===o.constructor.name?o.toString():r.decode(o,{stream:!0});(n=i.indexOf(e))>-1;)yield i.slice(0,n),i=i.slice(n+e.length)}async function*xe(t,e,n){var r,i=!0,o=null;try{yield await Promise.resolve();var a,s=(r=await(t.context.fetch||fetch)(e,n)).headers.get("Content-Type")||"";for await(var c of/multipart\/mixed/i.test(s)?async function*(t,e){var n,r=t.match(ge),i="--"+(r?r[1]:"-"),o=!0;for await(var a of we(_e(e),"\r\n"+i)){if(o){o=!1;var s=a.indexOf(i);if(!(s>-1))continue;a=a.slice(s+i.length)}try{yield n=JSON.parse(a.slice(a.indexOf("\r\n\r\n")+4))}catch(c){if(!n)throw c}if(n&&!1===n.hasNext)break}n&&!1!==n.hasNext&&(yield{hasNext:!1})}(s,r):/text\/event-stream/i.test(s)?async function*(t){var e;for await(var n of we(_e(t),"\n\n")){var r=n.match(be);if(r){var i=r[1];try{yield e=JSON.parse(i)}catch(o){if(!e)throw o}if(e&&!1===e.hasNext)break}}e&&!1!==e.hasNext&&(yield{hasNext:!1})}(r):/text\//i.test(s)?async function*(t){var e=await t.text();try{var n=JSON.parse(e);0,yield n}catch(r){throw new Error(e)}}(r):async function*(t){yield JSON.parse(await t.text())}(r))c.pending&&!o?a=c.pending:c.pending&&(a=[...a,...c.pending]),o=o?me(o,c,r,a):he(t,c,r),i=!1,yield o,i=!0;o||(yield o=he(t,{},r))}catch(l){if(!i)throw l;yield((t,e,n)=>({operation:t,data:void 0,error:new Qt({networkError:e,response:n}),extensions:void 0,hasNext:!1,stale:!1}))(t,r&&(r.status<200||r.status>=300)&&r.statusText?new Error(r.statusText):l,r)}}var ke=(t,e)=>{if(Array.isArray(t))for(var n=0,r=t.length;n<r;n++)ke(t[n],e);else if("object"==typeof t&&null!==t)for(var i in t)"__typename"===i&&"string"==typeof t[i]?e.add(t[i]):ke(t[i],e);return e},Se=t=>{if("definitions"in t){for(var e=[],n=0,r=t.definitions.length;n<r;n++){var i=Se(t.definitions[n]);e.push(i)}return{...t,definitions:e}}if("directives"in t&&t.directives&&t.directives.length){for(var o=[],a={},s=0,c=t.directives.length;s<c;s++){var l=t.directives[s],u=l.name.value;"_"!==u[0]?o.push(l):u=u.slice(1),a[u]=l}t={...t,directives:o,_directives:a}}if("selectionSet"in t){var d=[],f=t.kind===J;if(t.selectionSet){for(var h=0,p=t.selectionSet.selections.length;h<p;h++){var m=t.selectionSet.selections[h];f=f||m.kind===X&&"__typename"===m.name.value&&!m.alias;var v=Se(m);d.push(v)}return f||d.push({kind:X,name:{kind:K,value:"__typename"},_generated:!0}),{...t,selectionSet:{...t.selectionSet,selections:d}}}}return t},Oe=new Map;function Ee(t){var e=e=>t(e);return e.toPromise=()=>{return t=It(1)(Tt(t=>!t.stale&&!t.hasNext)(e)),new Promise(e=>{var n,r=kt;t(t=>{0===t?Promise.resolve(n).then(e):0===t.tag?(r=t[0])(0):(n=t[0],r(0))})});var t},e.then=(t,n)=>e.toPromise().then(t,n),e.subscribe=t=>Ht(t)(e),e}function Ce(t,e,n){return{...e,kind:t,context:e.context?{...e.context,...n}:n||e.context}}var Te=(t,e)=>Ce(t.kind,t,{meta:{...t.context.meta,...e}}),Re=()=>{};var Ae,Ne,Pe,Me,Fe=({kind:t})=>"mutation"!==t&&"query"!==t,je=t=>{var e=(t=>{var e=ue(t),n=Oe.get(e.__key);return n||(Oe.set(e.__key,n=Se(e)),Object.defineProperty(n,"__key",{value:e.__key,enumerable:!1})),n})(t.query);if(e!==t.query){var n=Ce(t.kind,t);return n.query=e,n}return t},De=(t,e)=>t.reexecuteOperation(Ce(e.kind,e,{requestPolicy:"network-only"})),Ie=({dispatchDebug:t})=>t=>Tt(t=>!1)(t),qe=function t(e){var n=0,r=new Map,i=new Map,o=new Set,a=[],s={url:e.url,fetchSubscriptions:e.fetchSubscriptions,fetchOptions:e.fetchOptions,fetch:e.fetch,preferGetMethod:e.preferGetMethod,requestPolicy:e.requestPolicy||"cache-first"},c=function(){var t,e;return{source:jt(Wt(n=>(t=n.next,e=n.complete,xt))),next(e){t&&t(e)},complete(){e&&e()}}}();function l(t){"mutation"!==t.kind&&"teardown"!==t.kind&&o.has(t.key)||("teardown"===t.kind?o.delete(t.key):"mutation"!==t.kind&&o.add(t.key),c.next(t))}var u=!1;function d(t){if(t&&l(t),!u){for(u=!0;u&&(t=a.shift());)l(t);u=!1}}var f=t=>{var e=qt(Tt(e=>"teardown"===e.kind&&e.key===t.key)(c.source))(Tt(e=>e.operation.kind===t.kind&&e.operation.key===t.key&&(!e.operation.context._instance||e.operation.context._instance===t.context._instance))(y));return e="query"!==t.kind?Lt(t=>!!t.hasNext,!0)(e):Dt(e=>{var n=Bt(e);return e.stale||e.hasNext?n:Nt([n,Rt(()=>(e.stale=!0,e))(It(1)(Tt(e=>e.key===t.key)(c.source)))])})(e),jt(e="mutation"!==t.kind?Pt(()=>{o.delete(t.key),r.delete(t.key),i.delete(t.key),u=!1;for(var e=a.length-1;e>=0;e--)a[e].key===t.key&&a.splice(e,1);l(Ce("teardown",t,t.context))})(Mt(e=>{if(e.stale)if(e.hasNext)for(var n=0;n<a.length;n++){var i=a[n];if(i.key===e.operation.key){o.delete(i.key);break}}else o.delete(t.key);else e.hasNext||o.delete(t.key);r.set(t.key,e)})(e)):Ft(()=>{l(t)})(e))},h=this instanceof t?this:Object.create(t.prototype),p=Object.assign(h,{suspense:!!e.suspense,operations$:c.source,reexecuteOperation(t){if("teardown"===t.kind)d(t);else if("mutation"===t.kind)a.push(t),Promise.resolve().then(d);else if(i.has(t.key)){for(var e=!1,n=0;n<a.length;n++)a[n].key===t.key&&(a[n]=t,e=!0);e||o.has(t.key)&&"network-only"!==t.context.requestPolicy?(o.delete(t.key),Promise.resolve().then(d)):(a.push(t),Promise.resolve().then(d))}},createRequestOperation:(t,e,r)=>(r||(r={}),Ce(t,e,{_instance:"mutation"===t?n=n+1|0:void 0,...s,...r,requestPolicy:r.requestPolicy||s.requestPolicy,suspense:r.suspense||!1!==r.suspense&&p.suspense})),executeRequestOperation:t=>"mutation"===t.kind?Ee(f(t)):Ee(function(t){return e=>t()(e)}(()=>{var e=i.get(t.key);e||i.set(t.key,e=f(t)),e=Ft(()=>{d(t)})(e);var n=r.get(t.key);return"query"===t.kind&&n&&(n.stale||n.hasNext)?Dt(Bt)(Nt([e,Tt(e=>e===r.get(t.key))(Bt(n))])):e})),executeQuery(t,e){var n=p.createRequestOperation("query",t,e);return p.executeRequestOperation(n)},executeSubscription(t,e){var n=p.createRequestOperation("subscription",t,e);return p.executeRequestOperation(n)},executeMutation(t,e){var n=p.createRequestOperation("mutation",t,e);return p.executeRequestOperation(n)},readQuery(t,e,n){var r=null;return Ht(t=>{r=t})(p.query(t,e,n)).unsubscribe(),r},query:(t,e,n)=>p.executeQuery(de(t,e),n),subscription:(t,e,n)=>p.executeSubscription(de(t,e),n),mutation:(t,e,n)=>p.executeMutation(de(t,e),n)}),m=Re,v=(t=>({client:e,forward:n,dispatchDebug:r})=>t.reduceRight((t,n)=>n({client:e,forward:e=>jt(t(jt(e))),dispatchDebug(t){}}),n))(e.exchanges),y=jt(v({client:p,dispatchDebug:m,forward:Ie({dispatchDebug:m})})(c.source));return function(t){Ht(t=>{})(t)}(y),p},Le=0,Ue=[],ze=e,Be=ze.__b,We=ze.__r,He=ze.diffed,$e=ze.__c,Qe=ze.unmount,Ve=ze.__;function Ke(t,e){ze.__h&&ze.__h(Ne,t,Le||e),Le=0;var n=Ne.__H||(Ne.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function Ge(t){return Le=1,Je(hn,t)}function Je(t,e,n){var r=Ke(Ae++,2);if(r.t=t,!r.__c&&(r.__=[n?n(e):hn(void 0,e),function(t){var e=r.__N?r.__N[0]:r.__[0],n=r.t(e,t);e!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=Ne,!Ne.__f)){var i=function(t,e,n){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter(function(t){return!!t.__c});if(i.every(function(t){return!t.__N}))return!o||o.call(this,t,e,n);var a=r.__c.props!==t;return i.forEach(function(t){if(t.__N){var e=t.__[0];t.__=t.__N,t.__N=void 0,e!==t.__[0]&&(a=!0)}}),o&&o.call(this,t,e,n)||a};Ne.__f=!0;var o=Ne.shouldComponentUpdate,a=Ne.componentWillUpdate;Ne.componentWillUpdate=function(t,e,n){if(this.__e){var r=o;o=void 0,i(t,e,n),o=r}a&&a.call(this,t,e,n)},Ne.shouldComponentUpdate=i}return r.__N||r.__}function Xe(t,e){var n=Ke(Ae++,3);!ze.__s&&fn(n.__H,e)&&(n.__=t,n.u=e,Ne.__H.__h.push(n))}function Ye(t,e){var n=Ke(Ae++,4);!ze.__s&&fn(n.__H,e)&&(n.__=t,n.u=e,Ne.__h.push(n))}function Ze(t){return Le=5,en(function(){return{current:t}},[])}function tn(t,e,n){Le=6,Ye(function(){if("function"==typeof t){var n=t(e());return function(){t(null),n&&"function"==typeof n&&n()}}if(t)return t.current=e(),function(){return t.current=null}},null==n?n:n.concat(t))}function en(t,e){var n=Ke(Ae++,7);return fn(n.__H,e)&&(n.__=t(),n.__H=e,n.__h=t),n.__}function nn(t,e){return Le=8,en(function(){return t},e)}function rn(t){var e=Ne.context[t.__c],n=Ke(Ae++,9);return n.c=t,e?(null==n.__&&(n.__=!0,e.sub(Ne)),e.props.value):t.__}function on(t,e){ze.useDebugValue&&ze.useDebugValue(e?e(t):t)}function an(){var t=Ke(Ae++,11);if(!t.__){for(var e=Ne.__v;null!==e&&!e.__m&&null!==e.__;)e=e.__;var n=e.__m||(e.__m=[0,0]);t.__="P"+n[0]+"-"+n[1]++}return t.__}function sn(){for(var t;t=Ue.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(un),t.__H.__h.forEach(dn),t.__H.__h=[]}catch(e){t.__H.__h=[],ze.__e(e,t.__v)}}ze.__b=function(t){Ne=null,Be&&Be(t)},ze.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),Ve&&Ve(t,e)},ze.__r=function(t){We&&We(t),Ae=0;var e=(Ne=t.__c).__H;e&&(Pe===Ne?(e.__h=[],Ne.__h=[],e.__.forEach(function(t){t.__N&&(t.__=t.__N),t.u=t.__N=void 0})):(e.__h.forEach(un),e.__h.forEach(dn),e.__h=[],Ae=0)),Pe=Ne},ze.diffed=function(t){He&&He(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(1!==Ue.push(e)&&Me===ze.requestAnimationFrame||((Me=ze.requestAnimationFrame)||ln)(sn)),e.__H.__.forEach(function(t){t.u&&(t.__H=t.u),t.u=void 0})),Pe=Ne=null},ze.__c=function(t,e){e.some(function(t){try{t.__h.forEach(un),t.__h=t.__h.filter(function(t){return!t.__||dn(t)})}catch(n){e.some(function(t){t.__h&&(t.__h=[])}),e=[],ze.__e(n,t.__v)}}),$e&&$e(t,e)},ze.unmount=function(t){Qe&&Qe(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach(function(t){try{un(t)}catch(n){e=n}}),n.__H=void 0,e&&ze.__e(e,n.__v))};var cn="function"==typeof requestAnimationFrame;function ln(t){var e,n=function(){clearTimeout(r),cn&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,35);cn&&(e=requestAnimationFrame(n))}function un(t){var e=Ne,n=t.__c;"function"==typeof n&&(t.__c=void 0,n()),Ne=e}function dn(t){var e=Ne;t.__c=t.__(),Ne=e}function fn(t,e){return!t||t.length!==e.length||e.some(function(e,n){return e!==t[n]})}function hn(t,e){return"function"==typeof e?e(t):e}function pn(t,e){for(var n in e)t[n]=e[n];return t}function mn(t,e){for(var n in t)if("__source"!==n&&!(n in e))return!0;for(var r in e)if("__source"!==r&&t[r]!==e[r])return!0;return!1}function vn(t,e){var n=e(),r=Ge({t:{__:n,u:e}}),i=r[0].t,o=r[1];return Ye(function(){i.__=n,i.u=e,yn(i)&&o({t:i})},[t,n,e]),Xe(function(){return yn(i)&&o({t:i}),t(function(){yn(i)&&o({t:i})})},[t]),n}function yn(t){var e,n,r=t.u,i=t.__;try{var o=r();return!((e=i)===(n=o)&&(0!==e||1/e==1/n)||e!=e&&n!=n)}catch(a){return!0}}function gn(t){t()}function bn(t){return t}function _n(){return[!1,gn]}var wn=Ye;function xn(t,e){this.props=t,this.context=e}(xn.prototype=new x).isPureReactComponent=!0,xn.prototype.shouldComponentUpdate=function(t,e){return mn(this.props,t)||mn(this.state,e)};var kn=e.__b;e.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),kn&&kn(t)};var Sn="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function On(t){function e(e){var n=pn({},e);return delete n.ref,t(n,e.ref||null)}return e.$$typeof=Sn,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(t.displayName||t.name)+")",e}var En=function(t,e){return null==t?null:R(R(t).map(e))},Cn={map:En,forEach:En,count:function(t){return t?R(t).length:0},only:function(t){var e=R(t);if(1!==e.length)throw"Children.only";return e[0]},toArray:R},Tn=e.__e;e.__e=function(t,e,n,r){if(t.then)for(var i,o=e;o=o.__;)if((i=o.__c)&&i.__c)return null==e.__e&&(e.__e=n.__e,e.__k=n.__k),i.__c(t,e);Tn(t,e,n,r)};var Rn=e.unmount;function An(t,e,n){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(t){"function"==typeof t.__c&&t.__c()}),t.__c.__H=null),null!=(t=pn({},t)).__c&&(t.__c.__P===n&&(t.__c.__P=e),t.__c.__e=!0,t.__c=null),t.__k=t.__k&&t.__k.map(function(t){return An(t,e,n)})),t}function Nn(t,e,n){return t&&n&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(t){return Nn(t,e,n)}),t.__c&&t.__c.__P===e&&(t.__e&&n.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=n)),t}function Pn(){this.__u=0,this.o=null,this.__b=null}function Mn(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function Fn(){this.i=null,this.l=null}e.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&32&t.__u&&(t.type=null),Rn&&Rn(t)},(Pn.prototype=new x).__c=function(t,e){var n=e.__c,r=this;null==r.o&&(r.o=[]),r.o.push(n);var i=Mn(r.__v),o=!1,a=function(){o||(o=!0,n.__R=null,i?i(s):s())};n.__R=a;var s=function(){if(! --r.__u){if(r.state.__a){var t=r.state.__a;r.__v.__k[0]=Nn(t,t.__c.__P,t.__c.__O)}var e;for(r.setState({__a:r.__b=null});e=r.o.pop();)e.forceUpdate()}};r.__u++||32&e.__u||r.setState({__a:r.__b=r.__v.__k[0]}),t.then(a,a)},Pn.prototype.componentWillUnmount=function(){this.o=[]},Pn.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=An(this.__b,n,r.__O=r.__P)}this.__b=null}var i=e.__a&&b(w,null,t.fallback);return i&&(i.__u&=-33),[b(w,null,e.__a?null:t.children),i]};var jn=function(t,e,n){if(++n[1]===n[0]&&t.l.delete(e),t.props.revealOrder&&("t"!==t.props.revealOrder[0]||!t.l.size))for(n=t.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;t.i=n=n[2]}};function Dn(t){return this.getChildContext=function(){return t.context},t.children}function In(t){var e=this,n=t.h;if(e.componentWillUnmount=function(){U(null,e.v),e.v=null,e.h=null},e.h&&e.h!==n&&e.componentWillUnmount(),!e.v){for(var r=e.__v;null!==r&&!r.__m&&null!==r.__;)r=r.__;e.h=n,e.v={nodeType:1,parentNode:n,childNodes:[],__k:{__m:r.__m},contains:function(){return!0},insertBefore:function(t,n){this.childNodes.push(t),e.h.insertBefore(t,n)},removeChild:function(t){this.childNodes.splice(this.childNodes.indexOf(t)>>>1,1),e.h.removeChild(t)}}}U(b(Dn,{context:e.context},t.__v),e.v)}(Fn.prototype=new x).__a=function(t){var e=this,n=Mn(e.__v),r=e.l.get(t);return r[0]++,function(i){var o=function(){e.props.revealOrder?(r.push(i),jn(e,t,r)):i()};n?n(o):o()}},Fn.prototype.render=function(t){this.i=null,this.l=new Map;var e=R(t.children);t.revealOrder&&"b"===t.revealOrder[0]&&e.reverse();for(var n=e.length;n--;)this.l.set(e[n],this.i=[1,0,this.i]);return t.children},Fn.prototype.componentDidUpdate=Fn.prototype.componentDidMount=function(){var t=this;this.l.forEach(function(e,n){jn(t,n,e)})};var qn="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Ln=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Un=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,zn=/[A-Z0-9]/g,Bn="undefined"!=typeof document,Wn=function(t){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(t)};x.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(x.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var Hn=e.event;function $n(){}function Qn(){return this.cancelBubble}function Vn(){return this.defaultPrevented}e.event=function(t){return Hn&&(t=Hn(t)),t.persist=$n,t.isPropagationStopped=Qn,t.isDefaultPrevented=Vn,t.nativeEvent=t};var Kn,Gn={enumerable:!1,configurable:!0,get:function(){return this.class}},Jn=e.vnode;e.vnode=function(t){"string"==typeof t.type&&function(t){var e=t.props,n=t.type,r={},i=-1===n.indexOf("-");for(var o in e){var a=e[o];if(!("value"===o&&"defaultValue"in e&&null==a||Bn&&"children"===o&&"noscript"===n||"class"===o||"className"===o)){var s=o.toLowerCase();"defaultValue"===o&&"value"in e&&null==e.value?o="value":"download"===o&&!0===a?a="":"translate"===s&&"no"===a?a=!1:"o"===s[0]&&"n"===s[1]?"ondoubleclick"===s?o="ondblclick":"onchange"!==s||"input"!==n&&"textarea"!==n||Wn(e.type)?"onfocus"===s?o="onfocusin":"onblur"===s?o="onfocusout":Un.test(o)&&(o=s):s=o="oninput":i&&Ln.test(o)?o=o.replace(zn,"-$&").toLowerCase():null===a&&(a=void 0),"oninput"===s&&r[o=s]&&(o="oninputCapture"),r[o]=a}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=R(e.children).forEach(function(t){t.props.selected=-1!=r.value.indexOf(t.props.value)})),"select"==n&&null!=r.defaultValue&&(r.value=R(e.children).forEach(function(t){t.props.selected=r.multiple?-1!=r.defaultValue.indexOf(t.props.value):r.defaultValue==t.props.value})),e.class&&!e.className?(r.class=e.class,Object.defineProperty(r,"className",Gn)):(e.className&&!e.class||e.class&&e.className)&&(r.class=r.className=e.className),t.props=r}(t),t.$$typeof=qn,Jn&&Jn(t)};var Xn=e.__r;e.__r=function(t){Xn&&Xn(t),Kn=t.__c};var Yn=e.diffed;e.diffed=function(t){Yn&&Yn(t);var e=t.props,n=t.__e;null!=n&&"textarea"===t.type&&"value"in e&&e.value!==n.value&&(n.value=null==e.value?"":e.value),Kn=null};function Zn(t){return!!t&&t.$$typeof===qn}function tr(t){return Zn(t)?B.apply(null,arguments):t}var er=function(t,e){return t(e)},nr={useState:Ge,useId:an,useReducer:Je,useEffect:Xe,useLayoutEffect:Ye,useInsertionEffect:wn,useTransition:_n,useDeferredValue:bn,useSyncExternalStore:vn,startTransition:gn,useRef:Ze,useImperativeHandle:tn,useMemo:en,useCallback:nn,useContext:rn,useDebugValue:on,version:"18.3.1",Children:Cn,render:function(t,e,n){return null==e.__k&&(e.textContent=""),U(t,e),"function"==typeof n&&n(),t?t.__c:null},hydrate:function(t,e,n){return z(t,e),"function"==typeof n&&n(),t?t.__c:null},unmountComponentAtNode:function(t){return!!t.__k&&(U(null,t),!0)},createPortal:function(t,e){var n=b(In,{__v:t,h:e});return n.containerInfo=e,n},createElement:b,createContext:W,createFactory:function(t){return b.bind(null,t)},cloneElement:tr,createRef:function(){return{current:null}},Fragment:w,isValidElement:Zn,isElement:Zn,isFragment:function(t){return Zn(t)&&t.type===w},isMemo:function(t){return!!t&&!!t.displayName&&("string"==typeof t.displayName||t.displayName instanceof String)&&t.displayName.startsWith("Memo(")},findDOMNode:function(t){return t&&(t.base||1===t.nodeType&&t)||null},Component:x,PureComponent:xn,memo:function(t,e){function n(t){var n=this.props.ref,r=n==t.ref;return!r&&n&&(n.call?n(null):n.current=null),e?!e(this.props,t)||!r:mn(this.props,t)}function r(e){return this.shouldComponentUpdate=n,b(t,e)}return r.displayName="Memo("+(t.displayName||t.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r},forwardRef:On,flushSync:er,unstable_batchedUpdates:function(t,e){return t(e)},StrictMode:w,Suspense:Pn,SuspenseList:Fn,lazy:function(t){var e,n,r;function i(i){if(e||(e=t()).then(function(t){n=t.default||t},function(t){r=t}),r)throw r;if(!n)throw e;return b(n,i)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{ReactCurrentDispatcher:{current:{readContext:function(t){return Kn.__n[t.__c].props.value},useCallback:nn,useContext:rn,useDebugValue:on,useDeferredValue:bn,useEffect:Xe,useId:an,useImperativeHandle:tn,useInsertionEffect:wn,useLayoutEffect:Ye,useMemo:en,useReducer:Je,useRef:Ze,useState:Ge,useSyncExternalStore:vn,useTransition:_n}}}},rr=W({}),ir=rr.Provider;rr.Consumer,rr.displayName="UrqlContext";var or={fetching:!1,stale:!1,hasNext:!1,error:void 0,data:void 0,extensions:void 0,operation:void 0},ar=(t,e)=>t===e||!(!t||!e||t.key!==e.key),sr=(t,e)=>{var n={...t,...e,data:void 0!==e.data||e.error?e.data:t.data,fetching:!!e.fetching,stale:!!e.stale};return((t,e)=>{for(var n in t)if(!(n in e))return!0;for(var r in e)if("operation"===r?!ar(t[r],e[r]):t[r]!==e[r])return!0;return!1})(t,n)?n:t};function cr(t,e){t(e)}function lr(t){var e=rn(rr),n=(t=>{if(!t._react){var e=new Set,n=new Map;t.operations$&&Ht(t=>{"teardown"===t.kind&&e.has(t.key)&&(e.delete(t.key),n.delete(t.key))})(t.operations$),t._react={get:t=>n.get(t),set(t,r){e.delete(t),n.set(t,r)},dispose(t){e.add(t)}}}return t._react})(e),r=((t,e)=>e&&void 0!==e.suspense?!!e.suspense:t.suspense)(e,t.context),i=function(t,e){var n=Ze(void 0);return en(()=>{var r=de(t,e);return void 0!==n.current&&n.current.key===r.key?n.current:(n.current=r,r)},[t,e])}(t.query,t.variables),o=en(()=>{if(t.pause)return null;var o=e.executeQuery(i,{requestPolicy:t.requestPolicy,...t.context});return r?Mt(t=>{n.set(i.key,t)})(o):o},[n,e,i,r,t.pause,t.requestPolicy,t.context]),a=nn((t,e)=>{if(!t)return{fetching:!1};var r=n.get(i.key);if(r){if(e&&null!=r&&"then"in r)throw r}else{var o,a=Ht(t=>{r=t,o&&o(r)})(Lt(()=>e&&!o||!r||"hasNext"in r&&r.hasNext)(t));if(null==r&&e){var s=new Promise(t=>{o=t});throw n.set(i.key,s),s}a.unsubscribe()}return r||{fetching:!0}},[n,i]),s=[e,i,t.requestPolicy,t.context,t.pause],[c,l]=Ge(()=>[o,sr(or,a(o,r)),s]),u=c[1];return o!==c[0]&&((t,e)=>{for(var n=0,r=e.length;n<r;n++)if(t[n]!==e[n])return!0;return!1})(c[2],s)&&l([o,u=sr(c[1],a(o,r)),s]),Xe(()=>{var t=c[0],e=c[2][1],r=!1,i=t=>{r=!0,cr(l,e=>{var n=sr(e[1],t);return e[1]!==n?[e[0],n,e[2]]:e})};if(t){var o=Ht(i)(Pt(()=>{i({fetching:!1})})(t));return r||i({fetching:!0}),()=>{n.dispose(e.key),o.unsubscribe()}}i({fetching:!1})},[n,c[0],c[2][1]]),[u,nn(o=>{var a={requestPolicy:t.requestPolicy,...t.context,...o};cr(l,t=>[r?Mt(t=>{n.set(i.key,t)})(e.executeQuery(i,a)):e.executeQuery(i,a),t[1],s])},[e,n,i,r,t.requestPolicy,t.context,t.pause])]}const ur=qe({url:"https://localhost:5002/api/sdata/graphql",exchanges:[({forward:t,client:e,dispatchDebug:n})=>{var r=new Map,i=new Map,o=t=>"query"===t.kind&&"network-only"!==t.context.requestPolicy&&("cache-only"===t.context.requestPolicy||r.has(t.key));return n=>{var a=Rt(t=>{var n=r.get(t.key),i=n||he(t,{data:null});return i={...i,operation:Te(t,{cacheOutcome:n?"hit":"miss"})},"cache-and-network"===t.context.requestPolicy&&(i.stale=!0,De(e,t)),i})(Tt(t=>!Fe(t)&&o(t))(n)),s=Mt(t=>{var{operation:n}=t;if(n){var o,a=n.context.additionalTypenames||[];if("subscription"!==t.operation.kind&&(a=(o=t.data,[...ke(o,new Set)]).concat(a)),"mutation"===t.operation.kind||"subscription"===t.operation.kind){for(var s=new Set,c=0;c<a.length;c++){var l=a[c],u=i.get(l);for(var d of(u||i.set(l,u=new Set),u.values()))s.add(d);u.clear()}for(var f of s.values())r.has(f)&&(n=r.get(f).operation,r.delete(f),De(e,n))}else if("query"===n.kind&&t.data){r.set(n.key,t);for(var h=0;h<a.length;h++){var p=a[h],m=i.get(p);m||i.set(p,m=new Set),m.add(n.key)}}}})(t(Tt(t=>"query"!==t.kind||"cache-only"!==t.context.requestPolicy)(Rt(t=>Te(t,{cacheOutcome:"miss"}))(Nt([Rt(je)(Tt(t=>!Fe(t)&&!o(t))(n)),Tt(t=>Fe(t))(n)])))));return Nt([a,s])}},({forward:t,dispatchDebug:e})=>e=>{var n=At(t=>{var n=function(t){var e={query:void 0,documentId:void 0,operationName:fe(t.query),variables:t.variables||void 0,extensions:t.extensions};return!("documentId"in t.query)||!t.query.documentId||t.query.definitions&&t.query.definitions.length?t.extensions&&t.extensions.persistedQuery&&!t.extensions.persistedQuery.miss||(e.query=ce(t.query)):e.documentId=t.query.documentId,e}(t),r=((t,e)=>{var n="query"===t.kind&&t.context.preferGetMethod;if(!n||!e)return t.context.url;var r=ve(t.context.url);for(var i in e){var o=e[i];o&&r[1].set(i,"object"==typeof o?Yt(o):o)}var a=r.join("?");return a.length>2047&&"force"!==n?(t.context.preferGetMethod=!1,t.context.url):a})(t,n),i=ye(t,n),o=qt(Tt(e=>"teardown"===e.kind&&e.key===t.key)(e))(function(t,e,n){var r;return"undefined"!=typeof AbortController&&(n.signal=(r=new AbortController).signal),Pt(()=>{r&&r.abort()})(Tt(t=>!!t)(Ut(xe(t,e,n))))}(t,r,i));return o})(Tt(t=>"teardown"!==t.kind&&("subscription"!==t.kind||!!t.context.fetchSubscriptions))(e));return Nt([n,t(Tt(t=>"teardown"===t.kind||"subscription"===t.kind&&!t.context.fetchSubscriptions)(e))])}],fetch:async(t,e)=>fetch(t,{...e,credentials:"include",headers:{...e?.headers||{}}})}),dr=":root {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  line-height: 1.5;\r\n  font-weight: 400;\r\n  color-scheme: light;\r\n  color: #131722;\r\n  background-color: #ffffff;\r\n  font-synthesis: none;\r\n  text-rendering: optimizeLegibility;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  --color-border: #e1e3e6;\r\n}\r\n\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  min-width: 320px;\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n  color: #131722;\r\n}\r\n\r\nbutton {\r\n  border: 1px solid transparent;\r\n  padding: 0.6em 1.2em;\r\n  font-size: 1em;\r\n  font-weight: 500;\r\n  font-family: inherit;\r\n  background-color: #ffffff;\r\n  color: #131722;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  border: 1px solid --color-border;\r\n}\r\n\r\nbutton:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\nbutton:focus,\r\nbutton:focus-visible {\r\n  outline: 2px solid #2962ff;\r\n  outline-offset: 2px;\r\n}\r\n\r\ninput {\r\n  font-family: inherit;\r\n  font-size: 1em;\r\n}\r\n\r\n\r\n.watchlist-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}\r\n\r\n.add-instrument-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}";class fr{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){const e={listener:t};return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}const hr="undefined"==typeof window||"Deno"in window;function pr(){}function mr(t){return"number"==typeof t&&t>=0&&t!==1/0}function vr(t,e){return Math.max(t+(e||0)-Date.now(),0)}function yr(t,e,n){return Ar(t)?"function"==typeof e?{...n,queryKey:t,queryFn:e}:{...e,queryKey:t}:t}function gr(t,e,n){return Ar(t)?[{...e,queryKey:t},n]:[t||{},e]}function br(t,e){const{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:a,stale:s}=t;if(Ar(a))if(r){if(e.queryHash!==wr(a,e.options))return!1}else if(!kr(e.queryKey,a))return!1;if("all"!==n){const t=e.isActive();if("active"===n&&!t)return!1;if("inactive"===n&&t)return!1}return("boolean"!=typeof s||e.isStale()===s)&&((void 0===i||i===e.state.fetchStatus)&&!(o&&!o(e)))}function _r(t,e){const{exact:n,fetching:r,predicate:i,mutationKey:o}=t;if(Ar(o)){if(!e.options.mutationKey)return!1;if(n){if(xr(e.options.mutationKey)!==xr(o))return!1}else if(!kr(e.options.mutationKey,o))return!1}return("boolean"!=typeof r||"loading"===e.state.status===r)&&!(i&&!i(e))}function wr(t,e){return((null==e?void 0:e.queryKeyHashFn)||xr)(t)}function xr(t){return JSON.stringify(t,(t,e)=>Tr(e)?Object.keys(e).sort().reduce((t,n)=>(t[n]=e[n],t),{}):e)}function kr(t,e){return Sr(t,e)}function Sr(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some(n=>!Sr(t[n],e[n])))}function Or(t,e){if(t===e)return t;const n=Cr(t)&&Cr(e);if(n||Tr(t)&&Tr(e)){const r=n?t.length:Object.keys(t).length,i=n?e:Object.keys(e),o=i.length,a=n?[]:{};let s=0;for(let c=0;c<o;c++){const r=n?c:i[c];a[r]=Or(t[r],e[r]),a[r]===t[r]&&s++}return r===o&&s===r?t:a}return e}function Er(t,e){if(t&&!e||e&&!t)return!1;for(const n in t)if(t[n]!==e[n])return!1;return!0}function Cr(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Tr(t){if(!Rr(t))return!1;const e=t.constructor;if(void 0===e)return!0;const n=e.prototype;return!!Rr(n)&&!!n.hasOwnProperty("isPrototypeOf")}function Rr(t){return"[object Object]"===Object.prototype.toString.call(t)}function Ar(t){return Array.isArray(t)}function Nr(t){return new Promise(e=>{setTimeout(e,t)})}function Pr(t){Nr(0).then(t)}function Mr(t,e,n){return null!=n.isDataEqual&&n.isDataEqual(t,e)?t:"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?Or(t,e):e}const Fr=new class extends fr{constructor(){super(),this.setup=t=>{if(!hr&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),window.addEventListener("focus",e,!1),()=>{window.removeEventListener("visibilitychange",e),window.removeEventListener("focus",e)}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)}setEventListener(t){var e;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.focused!==t&&(this.focused=t,this.onFocus())}onFocus(){this.listeners.forEach(({listener:t})=>{t()})}isFocused(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)}},jr=["online","offline"];const Dr=new class extends fr{constructor(){super(),this.setup=t=>{if(!hr&&window.addEventListener){const e=()=>t();return jr.forEach(t=>{window.addEventListener(t,e,!1)}),()=>{jr.forEach(t=>{window.removeEventListener(t,e)})}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)}setEventListener(t){var e;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(t=>{"boolean"==typeof t?this.setOnline(t):this.onOnline()})}setOnline(t){this.online!==t&&(this.online=t,this.onOnline())}onOnline(){this.listeners.forEach(({listener:t})=>{t()})}isOnline(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine}};function Ir(t){return Math.min(1e3*2**t,3e4)}function qr(t){return"online"!==(null!=t?t:"online")||Dr.isOnline()}class Lr{constructor(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}}function Ur(t){return t instanceof Lr}function zr(t){let e,n,r,i=!1,o=0,a=!1;const s=new Promise((t,e)=>{n=t,r=e}),c=()=>!Fr.isFocused()||"always"!==t.networkMode&&!Dr.isOnline(),l=r=>{a||(a=!0,null==t.onSuccess||t.onSuccess(r),null==e||e(),n(r))},u=n=>{a||(a=!0,null==t.onError||t.onError(n),null==e||e(),r(n))},d=()=>new Promise(n=>{e=t=>{const e=a||!c();return e&&n(t),e},null==t.onPause||t.onPause()}).then(()=>{e=void 0,a||null==t.onContinue||t.onContinue()}),f=()=>{if(a)return;let e;try{e=t.fn()}catch(n){e=Promise.reject(n)}Promise.resolve(e).then(l).catch(e=>{var n,r;if(a)return;const s=null!=(n=t.retry)?n:3,l=null!=(r=t.retryDelay)?r:Ir,h="function"==typeof l?l(o,e):l,p=!0===s||"number"==typeof s&&o<s||"function"==typeof s&&s(o,e);!i&&p?(o++,null==t.onFail||t.onFail(o,e),Nr(h).then(()=>{if(c())return d()}).then(()=>{i?u(e):f()})):u(e)})};return qr(t.networkMode)?f():d().then(f),{promise:s,cancel:e=>{a||(u(new Lr(e)),null==t.abort||t.abort())},continue:()=>(null==e?void 0:e())?s:Promise.resolve(),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1}}}const Br=console;const Wr=function(){let t=[],e=0,n=t=>{t()},r=t=>{t()};const i=r=>{e?t.push(r):Pr(()=>{n(r)})},o=()=>{const e=t;t=[],e.length&&Pr(()=>{r(()=>{e.forEach(t=>{n(t)})})})};return{batch:t=>{let n;e++;try{n=t()}finally{e--,e||o()}return n},batchCalls:t=>(...e)=>{i(()=>{t(...e)})},schedule:i,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{r=t}}}();class Hr{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),mr(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(t){this.cacheTime=Math.max(this.cacheTime||0,null!=t?t:hr?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}class $r extends Hr{constructor(t){super(),this.abortSignalConsumed=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.logger=t.logger||Br,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==e,r=n?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:n?null!=r?r:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"loading",fetchStatus:"idle"}}(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.cache.remove(this)}setData(t,e){const n=Mr(this.state.data,t,this.options);return this.dispatch({data:n,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),n}setState(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})}cancel(t){var e;const n=this.promise;return null==(e=this.retryer)||e.cancel(t),n?n.then(pr).catch(pr):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(t=>!1!==t.options.enabled)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(t=>t.getCurrentResult().isStale)}isStaleByTime(t=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!vr(this.state.dataUpdatedAt,t)}onFocus(){var t;const e=this.observers.find(t=>t.shouldFetchOnWindowFocus());e&&e.refetch({cancelRefetch:!1}),null==(t=this.retryer)||t.continue()}onOnline(){var t;const e=this.observers.find(t=>t.shouldFetchOnReconnect());e&&e.refetch({cancelRefetch:!1}),null==(t=this.retryer)||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(t,e){var n,r;if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&null!=e&&e.cancelRefetch)this.cancel({silent:!0});else if(this.promise){var i;return null==(i=this.retryer)||i.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}Array.isArray(this.options.queryKey);const o=function(){if("function"==typeof AbortController)return new AbortController}(),a={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},s=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>{if(o)return this.abortSignalConsumed=!0,o.signal}})};s(a);const c={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(a)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'")};var l;(s(c),null==(n=this.options.behavior)||n.onFetch(c),this.revertState=this.state,"idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(r=c.fetchOptions)?void 0:r.meta))&&this.dispatch({type:"fetch",meta:null==(l=c.fetchOptions)?void 0:l.meta});const u=t=>{var e,n,r,i;(Ur(t)&&t.silent||this.dispatch({type:"error",error:t}),Ur(t))||(null==(e=(n=this.cache.config).onError)||e.call(n,t,this),null==(r=(i=this.cache.config).onSettled)||r.call(i,this.state.data,t,this));this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=zr({fn:c.fetchFn,abort:null==o?void 0:o.abort.bind(o),onSuccess:t=>{var e,n,r,i;void 0!==t?(this.setData(t),null==(e=(n=this.cache.config).onSuccess)||e.call(n,t,this),null==(r=(i=this.cache.config).onSettled)||r.call(i,t,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):u(new Error(this.queryHash+" data is undefined"))},onError:u,onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(t){this.state=(e=>{var n,r;switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null!=(n=t.meta)?n:null,fetchStatus:qr(this.options.networkMode)?"fetching":"paused",...!e.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(r=t.dataUpdatedAt)?r:Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const i=t.error;return Ur(i)&&i.revert&&this.revertState?{...this.revertState}:{...e,error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),Wr.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate(t)}),this.cache.notify({query:this,type:"updated",action:t})})}}class Qr extends fr{constructor(t){super(),this.config=t||{},this.queries=[],this.queriesMap={}}build(t,e,n){var r;const i=e.queryKey,o=null!=(r=e.queryHash)?r:wr(i,e);let a=this.get(o);return a||(a=new $r({cache:this,logger:t.getLogger(),queryKey:i,queryHash:o,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(i)}),this.add(a)),a}add(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"added",query:t}))}remove(t){const e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter(e=>e!==t),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"removed",query:t}))}clear(){Wr.batch(()=>{this.queries.forEach(t=>{this.remove(t)})})}get(t){return this.queriesMap[t]}getAll(){return this.queries}find(t,e){const[n]=gr(t,e);return void 0===n.exact&&(n.exact=!0),this.queries.find(t=>br(n,t))}findAll(t,e){const[n]=gr(t,e);return Object.keys(n).length>0?this.queries.filter(t=>br(n,t)):this.queries}notify(t){Wr.batch(()=>{this.listeners.forEach(({listener:e})=>{e(t)})})}onFocus(){Wr.batch(()=>{this.queries.forEach(t=>{t.onFocus()})})}onOnline(){Wr.batch(()=>{this.queries.forEach(t=>{t.onOnline()})})}}class Vr extends Hr{constructor(t){super(),this.defaultOptions=t.defaultOptions,this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.logger=t.logger||Br,this.observers=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(t){this.dispatch({type:"setState",state:t})}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.observers=this.observers.filter(e=>e!==t),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var t,e;return null!=(t=null==(e=this.retryer)?void 0:e.continue())?t:this.execute()}async execute(){const t=()=>{var t;return this.retryer=zr({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise},e="loading"===this.state.status;try{var n,r,i,o,a,s,c,l;if(!e){var u,d,f,h;this.dispatch({type:"loading",variables:this.options.variables}),await(null==(u=(d=this.mutationCache.config).onMutate)?void 0:u.call(d,this.state.variables,this));const t=await(null==(f=(h=this.options).onMutate)?void 0:f.call(h,this.state.variables));t!==this.state.context&&this.dispatch({type:"loading",context:t,variables:this.state.variables})}const p=await t();return await(null==(n=(r=this.mutationCache.config).onSuccess)?void 0:n.call(r,p,this.state.variables,this.state.context,this)),await(null==(i=(o=this.options).onSuccess)?void 0:i.call(o,p,this.state.variables,this.state.context)),await(null==(a=(s=this.mutationCache.config).onSettled)?void 0:a.call(s,p,null,this.state.variables,this.state.context,this)),await(null==(c=(l=this.options).onSettled)?void 0:c.call(l,p,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:p}),p}catch(x){try{var p,m,v,y,g,b,_,w;throw await(null==(p=(m=this.mutationCache.config).onError)?void 0:p.call(m,x,this.state.variables,this.state.context,this)),await(null==(v=(y=this.options).onError)?void 0:v.call(y,x,this.state.variables,this.state.context)),await(null==(g=(b=this.mutationCache.config).onSettled)?void 0:g.call(b,void 0,x,this.state.variables,this.state.context,this)),await(null==(_=(w=this.options).onSettled)?void 0:_.call(w,void 0,x,this.state.variables,this.state.context)),x}finally{this.dispatch({type:"error",error:x})}}}dispatch(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"loading":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!qr(this.options.networkMode),status:"loading",variables:t.variables};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"};case"setState":return{...e,...t.state}}})(this.state),Wr.batch(()=>{this.observers.forEach(e=>{e.onMutationUpdate(t)}),this.mutationCache.notify({mutation:this,type:"updated",action:t})})}}class Kr extends fr{constructor(t){super(),this.config=t||{},this.mutations=[],this.mutationId=0}build(t,e,n){const r=new Vr({mutationCache:this,logger:t.getLogger(),mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:n,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0});return this.add(r),r}add(t){this.mutations.push(t),this.notify({type:"added",mutation:t})}remove(t){this.mutations=this.mutations.filter(e=>e!==t),this.notify({type:"removed",mutation:t})}clear(){Wr.batch(()=>{this.mutations.forEach(t=>{this.remove(t)})})}getAll(){return this.mutations}find(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find(e=>_r(t,e))}findAll(t){return this.mutations.filter(e=>_r(t,e))}notify(t){Wr.batch(()=>{this.listeners.forEach(({listener:e})=>{e(t)})})}resumePausedMutations(){var t;return this.resuming=(null!=(t=this.resuming)?t:Promise.resolve()).then(()=>{const t=this.mutations.filter(t=>t.state.isPaused);return Wr.batch(()=>t.reduce((t,e)=>t.then(()=>e.continue().catch(pr)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}function Gr(){return{onFetch:t=>{t.fetchFn=()=>{var e,n,r,i,o,a;const s=null==(e=t.fetchOptions)||null==(n=e.meta)?void 0:n.refetchPage,c=null==(r=t.fetchOptions)||null==(i=r.meta)?void 0:i.fetchMore,l=null==c?void 0:c.pageParam,u="forward"===(null==c?void 0:c.direction),d="backward"===(null==c?void 0:c.direction),f=(null==(o=t.state.data)?void 0:o.pages)||[],h=(null==(a=t.state.data)?void 0:a.pageParams)||[];let p=h,m=!1;const v=t.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+t.options.queryHash+"'")),y=(t,e,n,r)=>(p=r?[e,...p]:[...p,e],r?[n,...t]:[...t,n]),g=(e,n,r,i)=>{if(m)return Promise.reject("Cancelled");if(void 0===r&&!n&&e.length)return Promise.resolve(e);const o={queryKey:t.queryKey,pageParam:r,meta:t.options.meta};var a;a=o,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>{var e,n;return null!=(e=t.signal)&&e.aborted?m=!0:null==(n=t.signal)||n.addEventListener("abort",()=>{m=!0}),t.signal}});const s=v(o);return Promise.resolve(s).then(t=>y(e,r,t,i))};let b;if(f.length)if(u){const e=void 0!==l,n=e?l:Jr(t.options,f);b=g(f,e,n)}else if(d){const e=void 0!==l,n=e?l:(_=t.options,w=f,null==_.getPreviousPageParam?void 0:_.getPreviousPageParam(w[0],w));b=g(f,e,n,!0)}else{p=[];const e=void 0===t.options.getNextPageParam;b=!s||!f[0]||s(f[0],0,f)?g([],e,h[0]):Promise.resolve(y([],h[0],f[0]));for(let n=1;n<f.length;n++)b=b.then(r=>{if(!s||!f[n]||s(f[n],n,f)){const i=e?h[n]:Jr(t.options,r);return g(r,e,i)}return Promise.resolve(y(r,h[n],f[n]))})}else b=g([]);var _,w;const x=b.then(t=>({pages:t,pageParams:p}));return x}}}}function Jr(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}function Xr(t,e){return function(t,e){return!(!1===e.enabled||t.state.dataUpdatedAt||"error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&Yr(t,e,e.refetchOnMount)}function Yr(t,e,n){if(!1!==e.enabled){const r="function"==typeof n?n(t):n;return"always"===r||!1!==r&&ti(t,e)}return!1}function Zr(t,e,n,r){return!1!==n.enabled&&(t!==e||!1===r.enabled)&&(!n.suspense||"error"!==t.state.status)&&ti(t,n)}function ti(t,e){return t.isStaleByTime(e.staleTime)}let ei=class extends fr{constructor(t,e){super(),this.client=t,this.setOptions(e),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;const n=this.options;this.options=this.client.defaultMutationOptions(t),Er(n,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(e=this.currentMutation)||e.setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.currentMutation)||t.removeObserver(this))}onMutationUpdate(t){this.updateResult();const e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==t?t:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const t=this.currentMutation?this.currentMutation.state:{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0},e={...t,isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset};this.currentResult=e}notify(t){Wr.batch(()=>{var e,n,r,i;if(this.mutateOptions&&this.hasListeners())if(t.onSuccess)null==(e=(n=this.mutateOptions).onSuccess)||e.call(n,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(i=this.mutateOptions).onSettled)||r.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(t.onError){var o,a,s,c;null==(o=(a=this.mutateOptions).onError)||o.call(a,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(s=(c=this.mutateOptions).onSettled)||s.call(c,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}t.listeners&&this.listeners.forEach(({listener:t})=>{t(this.currentResult)})})}};function ni(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var ri=Symbol.for("preact-signals");function ii(){if(li>1)li--;else{for(var t,e=!1;void 0!==ci;){var n=ci;for(ci=void 0,ui++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&mi(n))try{n.c()}catch(i){e||(t=i,e=!0)}n=r}}if(ui=0,li--,e)throw t}}function oi(t){if(li>0)return t();li++;try{return t()}finally{ii()}}var ai=void 0;function si(t){var e=ai;ai=void 0;try{return t()}finally{ai=e}}var ci=void 0,li=0,ui=0,di=0;function fi(t){if(void 0!==ai){var e=t.n;if(void 0===e||e.t!==ai)return e={i:0,S:t,p:ai.s,n:void 0,t:ai,e:void 0,x:void 0,r:e},void 0!==ai.s&&(ai.s.n=e),ai.s=e,t.n=e,32&ai.f&&t.S(e),e;if(-1===e.i)return e.i=0,void 0!==e.n&&(e.n.p=e.p,void 0!==e.p&&(e.p.n=e.n),e.p=ai.s,e.n=void 0,ai.s.n=e,ai.s=e),e}}function hi(t,e){this.v=t,this.i=0,this.n=void 0,this.t=void 0,this.W=null==e?void 0:e.watched,this.Z=null==e?void 0:e.unwatched}function pi(t,e){return new hi(t,e)}function mi(t){for(var e=t.s;void 0!==e;e=e.n)if(e.S.i!==e.i||!e.S.h()||e.S.i!==e.i)return!0;return!1}function vi(t){for(var e=t.s;void 0!==e;e=e.n){var n=e.S.n;if(void 0!==n&&(e.r=n),e.S.n=e,e.i=-1,void 0===e.n){t.s=e;break}}}function yi(t){for(var e=t.s,n=void 0;void 0!==e;){var r=e.p;-1===e.i?(e.S.U(e),void 0!==r&&(r.n=e.n),void 0!==e.n&&(e.n.p=r)):n=e,e.S.n=e.r,void 0!==e.r&&(e.r=void 0),e=r}t.s=n}function gi(t,e){hi.call(this,void 0),this.x=t,this.s=void 0,this.g=di-1,this.f=4,this.W=null==e?void 0:e.watched,this.Z=null==e?void 0:e.unwatched}function bi(t,e){return new gi(t,e)}function _i(t){var e=t.u;if(t.u=void 0,"function"==typeof e){li++;var n=ai;ai=void 0;try{e()}catch(r){throw t.f&=-2,t.f|=8,wi(t),r}finally{ai=n,ii()}}}function wi(t){for(var e=t.s;void 0!==e;e=e.n)e.S.U(e);t.x=void 0,t.s=void 0,_i(t)}function xi(t){if(ai!==this)throw new Error("Out-of-order effect");yi(this),ai=t,this.f&=-2,8&this.f&&wi(this),ii()}function ki(t){this.x=t,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function Si(t){var e=new ki(t);try{e.c()}catch(r){throw e.d(),r}var n=e.d.bind(e);return n[Symbol.dispose]=n,n}hi.prototype.brand=ri,hi.prototype.h=function(){return!0},hi.prototype.S=function(t){var e=this,n=this.t;n!==t&&void 0===t.e&&(t.x=n,this.t=t,void 0!==n?n.e=t:si(function(){var t;null==(t=e.W)||t.call(e)}))},hi.prototype.U=function(t){var e=this;if(void 0!==this.t){var n=t.e,r=t.x;void 0!==n&&(n.x=r,t.e=void 0),void 0!==r&&(r.e=n,t.x=void 0),t===this.t&&(this.t=r,void 0===r&&si(function(){var t;null==(t=e.Z)||t.call(e)}))}},hi.prototype.subscribe=function(t){var e=this;return Si(function(){var n=e.value,r=ai;ai=void 0;try{t(n)}finally{ai=r}})},hi.prototype.valueOf=function(){return this.value},hi.prototype.toString=function(){return this.value+""},hi.prototype.toJSON=function(){return this.value},hi.prototype.peek=function(){var t=ai;ai=void 0;try{return this.value}finally{ai=t}},Object.defineProperty(hi.prototype,"value",{get:function(){var t=fi(this);return void 0!==t&&(t.i=this.i),this.v},set:function(t){if(t!==this.v){if(ui>100)throw new Error("Cycle detected");this.v=t,this.i++,di++,li++;try{for(var e=this.t;void 0!==e;e=e.x)e.t.N()}finally{ii()}}}}),gi.prototype=new hi,gi.prototype.h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===di)return!0;if(this.g=di,this.f|=1,this.i>0&&!mi(this))return this.f&=-2,!0;var t=ai;try{vi(this),ai=this;var e=this.x();(16&this.f||this.v!==e||0===this.i)&&(this.v=e,this.f&=-17,this.i++)}catch(n){this.v=n,this.f|=16,this.i++}return ai=t,yi(this),this.f&=-2,!0},gi.prototype.S=function(t){if(void 0===this.t){this.f|=36;for(var e=this.s;void 0!==e;e=e.n)e.S.S(e)}hi.prototype.S.call(this,t)},gi.prototype.U=function(t){if(void 0!==this.t&&(hi.prototype.U.call(this,t),void 0===this.t)){this.f&=-33;for(var e=this.s;void 0!==e;e=e.n)e.S.U(e)}},gi.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var t=this.t;void 0!==t;t=t.x)t.t.N()}},Object.defineProperty(gi.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var t=fi(this);if(this.h(),void 0!==t&&(t.i=this.i),16&this.f)throw this.v;return this.v}}),ki.prototype.c=function(){var t=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var e=this.x();"function"==typeof e&&(this.u=e)}finally{t()}},ki.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,_i(this),vi(this),li++;var t=ai;return ai=this,xi.bind(this,t)},ki.prototype.N=function(){2&this.f||(this.f|=2,this.o=ci,ci=this)},ki.prototype.d=function(){this.f|=8,1&this.f||wi(this)},ki.prototype.dispose=function(){this.d()};var Oi,Ei,Ci,Ti=[],Ri=[];function Ai(t,n){e[t]=n.bind(null,e[t]||function(){})}function Ni(t){Ci&&Ci(),Ci=t&&t.S()}function Pi(t){var e=this,n=t.data,i=Fi(n);i.value=n;var o=en(function(){for(var t=e,n=e.__v;n=n.__;)if(n.__c){n.__c.__$f|=4;break}var o=bi(function(){var t=i.value.value;return 0===t?0:!0===t?"":t||""}),a=bi(function(){return!Array.isArray(o.value)&&!r(o.value)}),s=Si(function(){if(this.N=zi,a.value){var e=o.value;t.__v&&t.__v.__e&&3===t.__v.__e.nodeType&&(t.__v.__e.data=e)}}),c=e.__$u.d;return e.__$u.d=function(){s(),c.call(this)},[a,o]},[]),a=o[0],s=o[1];return a.value?s.peek():s.value}function Mi(t,e,n,r){var i=e in t&&void 0===t.ownerSVGElement,o=pi(n);return{o:function(t,e){o.value=t,r=e},d:Si(function(){this.N=zi;var n=o.value.value;r[e]!==n&&(r[e]=n,i?t[e]=n:n?t.setAttribute(e,n):t.removeAttribute(e))})}}function Fi(t,e){return en(function(){return pi(t,e)},[])}function ji(t,e){var n=Ze(t);return n.current=t,Ei.__$f|=4,en(function(){return bi(function(){return n.current()},e)},[])}Si(function(){Oi=this.N})(),Pi.displayName="_st",Object.defineProperties(hi.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:Pi},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),Ai("__b",function(t,e){if("string"==typeof e.type){var n,r=e.props;for(var i in r)if("children"!==i){var o=r[i];o instanceof hi&&(n||(e.__np=n={}),n[i]=o,r[i]=o.peek())}}t(e)}),Ai("__r",function(t,e){if(e.type!==w){Ni();var n,r=e.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=(Si(function(){i=this}),i.c=function(){r.__$f|=1,r.setState({})},n=i))),Ei=r,Ni(n)}var i;t(e)}),Ai("__e",function(t,e,n,r){Ni(),Ei=void 0,t(e,n,r)}),Ai("diffed",function(t,e){var n;if(Ni(),Ei=void 0,"string"==typeof e.type&&(n=e.__e)){var r=e.__np,i=e.props;if(r){var o=n.U;if(o)for(var a in o){var s=o[a];void 0===s||a in r||(s.d(),o[a]=void 0)}else o={},n.U=o;for(var c in r){var l=o[c],u=r[c];void 0===l?(l=Mi(n,c,u,i),o[c]=l):l.o(u,i)}}}t(e)}),Ai("unmount",function(t,e){if("string"==typeof e.type){var n=e.__e;if(n){var r=n.U;if(r)for(var i in n.U=void 0,r){var o=r[i];o&&o.d()}}}else{var a=e.__c;if(a){var s=a.__$u;s&&(a.__$u=void 0,s.d())}}t(e)}),Ai("__h",function(t,e,n,r){(r<3||9===r)&&(e.__$f|=2),t(e,n,r)}),x.prototype.shouldComponentUpdate=function(t,e){var n=this.__$u,r=n&&void 0!==n.s;for(var i in e)return!0;if(this.__f||"boolean"==typeof this.u&&!0===this.u){var o=2&this.__$f;if(!(r||o||4&this.__$f))return!0;if(1&this.__$f)return!0}else{if(!(r||4&this.__$f))return!0;if(3&this.__$f)return!0}for(var a in t)if("__source"!==a&&t[a]!==this.props[a])return!0;for(var s in this.props)if(!(s in t))return!0;return!1};var Di="undefined"==typeof requestAnimationFrame?setTimeout:function(t){var e=function(){clearTimeout(n),cancelAnimationFrame(r),t()},n=setTimeout(e,35),r=requestAnimationFrame(e)},Ii=function(t){queueMicrotask(function(){queueMicrotask(t)})};function qi(){oi(function(){for(var t;t=Ti.shift();)Oi.call(t)})}function Li(){1===Ti.push(this)&&(e.requestAnimationFrame||Di)(qi)}function Ui(){oi(function(){for(var t;t=Ri.shift();)Oi.call(t)})}function zi(){1===Ri.push(this)&&(e.requestAnimationFrame||Ii)(Ui)}function Bi(t){var e=Ze(t);e.current=t,Xe(function(){return Si(function(){return this.N=Li,e.current()})},[])}const Wi=Object.freeze(Object.defineProperty({__proto__:null,Signal:hi,batch:oi,computed:bi,effect:Si,signal:pi,untracked:si,useComputed:ji,useSignal:Fi,useSignalEffect:Bi},Symbol.toStringTag,{value:"Module"}));var Hi,$i,Qi;const Vi=()=>{throw new Error("preact signals runtime not implemented hooks")},{Signal:Ki,batch:Gi,computed:Ji,effect:Xi,signal:Yi,untracked:Zi}=Wi,to=null!=(Hi=null==Wi?void 0:ji)?Hi:Vi,eo=null!=($i=null==Wi?void 0:Fi)?$i:Vi,no=null!=(Qi=null==Wi?void 0:Bi)?Qi:Vi,ro=t=>e=>{Gi(()=>{for(const n in e)t[n]=e[n]})},io=Symbol("store-state"),oo={get(t,e,n){if(e===io)return t[e];const r=t[io];if(e in r)return r[e]?.value;const i=Object.getOwnPropertyDescriptor(t,e);return i?.get?(r[e]=Ji(i.get?.bind(n)),delete t[e],r[e]?.value):"function"==typeof t[e]?t[e]:(r[e]=Yi(t[e]),delete t[e],r[e].value)},set(t,e,n){if("function"==typeof t[e])return t[e]=n,!0;const r=t[io];return e in t&&delete t[e],r[e]?(r[e].value=n,!0):(r[e]=Yi(n),!0)},deleteProperty(t,e){const n=e in t?t:e in t[io]?t[io]:null;return!!n&&(delete n[e],!0)},has:(t,e)=>e in t||e in t[io],ownKeys:t=>[...Object.keys(t),...Object.keys(t[io])],getOwnPropertyDescriptor:()=>({enumerable:!0,configurable:!0})},ao=t=>{const e=(t=>t[io]?t:(t[io]={},new Proxy(t,oo)))(t);return[e,ro(e)]};var so,co;function lo(t){this[so.Accessor]=t}function uo(t,e){this[so.Accessor]=t,this[so.Setter]=e}(co=so||(so={})).Accessor="_a",co.Setter="_s",lo.prototype=Object.create(Ki.prototype),uo.prototype=Object.create(lo.prototype),Object.defineProperties(lo.prototype,{value:{get(){return this[so.Accessor]()},set(){throw new Error("Uncached value is readonly")}},peek:{value(){return Zi(()=>this[so.Accessor]())}},valueOf:{value(){return this[so.Accessor]()}},toString:{value(){return String(this[so.Accessor]())}}}),Object.defineProperty(uo.prototype,"value",{get(){return this[so.Accessor]()},set(t){this[so.Setter](t)}});const fo=t=>new lo(t),ho=[],po=t=>to(()=>{return"function"==typeof(e=t)?e():e.value;var e}),mo=t=>{const e=eo(t);return e.peek()!==t&&(e.value=t),e},vo=t=>mo(rn(t)),yo=t=>{Xe(()=>Xi(t),ho)},go=t=>{const e=Ze(null);return null===e.current&&(e.current=Ji(t)),e.current},bo=W(void 0),_o=W(!1);function wo(t,e){return t||(e&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=bo),window.ReactQueryClientContext):bo)}const xo=t=>mo((({context:t}={})=>{const e=rn(wo(t,rn(_o)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e})(t)),ko=({client:t,children:e,context:n,contextSharing:r=!1})=>{Xe(()=>(t.mount(),()=>{t.unmount()}),[t]);const i=wo(n,r);return b(_o.Provider,{value:!n&&r},b(i.Provider,{value:t},e))},So=W(!1);So.Provider;const Oo=W(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}());function Eo(t,e){return"function"==typeof t?t(...e):!!t}const Co=t=>{const e=po(t),[n,r]=(t=>{const e=Ze();return e.current||(e.current=ao(Zi(t))),e.current})(()=>e.value.getCurrent());return yo(()=>{r(e.value.getCurrent())}),yo(()=>e.value.subscribe(r)),n},To=[],Ro={apply:(t,e,n)=>Zi(()=>t.apply(e,n))},Ao=t=>new Proxy(t,Ro),No=t=>{for(const e in t)"function"==typeof t[e]&&(t[e]=Ao(t[e]));return t},Po=t=>{const e=Ze(t);e.current=t;const n=nn(()=>e.current(),[t]),r=Ze(!0),i=eo(n);r.current&&(i.value=n);const o=to(()=>i.value());return no(()=>{r.current=o.value.executeOptionsOnReferenceChange??true}),o};var Mo,Fo;(Fo=Mo||(Mo={}))[Fo.Error=0]="Error",Fo[Fo.Success=1]="Success",Fo[Fo.Suspense=2]="Suspense";const jo=(Do=class extends fr{constructor(t,e){super(),this.client=t,this.options=e,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(e)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.currentQuery.addObserver(this),Xr(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Yr(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Yr(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(t,e){const n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryOptions(t),Er(n,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();const i=this.hasListeners();i&&Zr(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(e),!i||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();const o=this.computeRefetchInterval();!i||this.currentQuery===r&&this.options.enabled===n.enabled&&o===this.currentRefetchInterval||this.updateRefetchInterval(o)}getOptimisticResult(t){const e=this.client.getQueryCache().build(this.client,t),n=this.createResult(e,t);return function(t,e,n){return!n.keepPreviousData&&(void 0!==n.placeholderData?e.isPlaceholderData:t.getCurrentResult()!==e)}(this,n,t)&&(this.currentResult=n,this.currentResultOptions=this.options,this.currentResultState=this.currentQuery.state),n}getCurrentResult(){return this.currentResult}trackResult(t){const e={};return Object.keys(t).forEach(n=>{Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(n),t[n])})}),e}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch({refetchPage:t,...e}={}){return this.fetch({...e,meta:{refetchPage:t}})}fetchOptimistic(t){const e=this.client.defaultQueryOptions(t),n=this.client.getQueryCache().build(this.client,e);return n.isFetchingOptimistic=!0,n.fetch().then(()=>this.createResult(n,e))}fetch(t){var e;return this.executeFetch({...t,cancelRefetch:null==(e=t.cancelRefetch)||e}).then(()=>(this.updateResult(),this.currentResult))}executeFetch(t){this.updateQuery();let e=this.currentQuery.fetch(this.options,t);return null!=t&&t.throwOnError||(e=e.catch(pr)),e}updateStaleTimeout(){if(this.clearStaleTimeout(),hr||this.currentResult.isStale||!mr(this.options.staleTime))return;const t=vr(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(()=>{this.currentResult.isStale||this.updateResult()},t)}computeRefetchInterval(){var t;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t}updateRefetchInterval(t){this.clearRefetchInterval(),this.currentRefetchInterval=t,!hr&&!1!==this.options.enabled&&mr(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(()=>{(this.options.refetchIntervalInBackground||Fr.isFocused())&&this.executeFetch()},this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(t,e){const n=this.currentQuery,r=this.options,i=this.currentResult,o=this.currentResultState,a=this.currentResultOptions,s=t!==n,c=s?t.state:this.currentQueryInitialState,l=s?this.currentResult:this.previousQueryResult,{state:u}=t;let d,{dataUpdatedAt:f,error:h,errorUpdatedAt:p,fetchStatus:m,status:v}=u,y=!1,g=!1;if(e._optimisticResults){const i=this.hasListeners(),o=!i&&Xr(t,e),a=i&&Zr(t,n,e,r);(o||a)&&(m=qr(t.options.networkMode)?"fetching":"paused",f||(v="loading")),"isRestoring"===e._optimisticResults&&(m="idle")}if(e.keepPreviousData&&!u.dataUpdatedAt&&null!=l&&l.isSuccess&&"error"!==v)d=l.data,f=l.dataUpdatedAt,v=l.status,y=!0;else if(e.select&&void 0!==u.data)if(i&&u.data===(null==o?void 0:o.data)&&e.select===this.selectFn)d=this.selectResult;else try{this.selectFn=e.select,d=e.select(u.data),d=Mr(null==i?void 0:i.data,d,e),this.selectResult=d,this.selectError=null}catch(x){this.selectError=x}else d=u.data;if(void 0!==e.placeholderData&&void 0===d&&"loading"===v){let t;if(null!=i&&i.isPlaceholderData&&e.placeholderData===(null==a?void 0:a.placeholderData))t=i.data;else if(t="function"==typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&void 0!==t)try{t=e.select(t),this.selectError=null}catch(x){this.selectError=x}void 0!==t&&(v="success",d=Mr(null==i?void 0:i.data,t,e),g=!0)}this.selectError&&(h=this.selectError,d=this.selectResult,p=Date.now(),v="error");const b="fetching"===m,_="loading"===v,w="error"===v;return{status:v,fetchStatus:m,isLoading:_,isSuccess:"success"===v,isError:w,isInitialLoading:_&&b,data:d,dataUpdatedAt:f,error:h,errorUpdatedAt:p,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>c.dataUpdateCount||u.errorUpdateCount>c.errorUpdateCount,isFetching:b,isRefetching:b&&!_,isLoadingError:w&&0===u.dataUpdatedAt,isPaused:"paused"===m,isPlaceholderData:g,isPreviousData:y,isRefetchError:w&&0!==u.dataUpdatedAt,isStale:ti(t,e),refetch:this.refetch,remove:this.remove}}updateResult(t){const e=this.currentResult,n=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,Er(n,e))return;this.currentResult=n;const r={cache:!0};!1!==(null==t?void 0:t.listeners)&&(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options;if("all"===t||!t&&!this.trackedProps.size)return!0;const n=new Set(null!=t?t:this.trackedProps);return this.options.useErrorBoundary&&n.add("error"),Object.keys(this.currentResult).some(t=>{const r=t;return this.currentResult[r]!==e[r]&&n.has(r)})})()&&(r.listeners=!0),this.notify({...r,...t})}updateQuery(){const t=this.client.getQueryCache().build(this.client,this.options);if(t===this.currentQuery)return;const e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}onQueryUpdate(t){const e={};"success"===t.type?e.onSuccess=!t.manual:"error"!==t.type||Ur(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()}notify(t){Wr.batch(()=>{var e,n,r,i;if(t.onSuccess)null==(e=(n=this.options).onSuccess)||e.call(n,this.currentResult.data),null==(r=(i=this.options).onSettled)||r.call(i,this.currentResult.data,null);else if(t.onError){var o,a,s,c;null==(o=(a=this.options).onError)||o.call(a,this.currentResult.error),null==(s=(c=this.options).onSettled)||s.call(c,void 0,this.currentResult.error)}t.listeners&&this.listeners.forEach(({listener:t})=>{t(this.currentResult)}),t.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})})}},t=>{const e=Po(t),n=xo({context:go(()=>e.value.context).value}),r=vo(So),i=vo(Oo),o=fo(()=>e.value.suspenseBehavior??"load-on-access"),a=go(()=>{const t=No(n.value.defaultQueryOptions(e.value));var o;return t._optimisticResults=r.value?"isRestoring":"optimistic",(o=t).suspense&&"number"!=typeof o.staleTime&&(o.staleTime=1e3),((t,e)=>{(t.suspense||t.useErrorBoundary)&&(e.isReset()||(t.retryOnMount=!1))})(t,i.value),t}),s=go(()=>new Do(n.value,a.peek()));yo(()=>{s.value.setOptions(a.value)});const c=Co(()=>({getCurrent:()=>s.value.getOptimisticResult(a.value),subscribe:t=>s.value.subscribe(e=>{t(e)})}));(t=>{const e=po(t);yo(()=>{e.value.clearReset()})})(i);const l=fo(()=>{return t=a.value,e=c,n=r.value,t?.suspense&&((t,e)=>t.isLoading&&t.isFetching&&!e)(e,n);var t,e,n}),u=()=>(({result:t,errorResetBoundary:e,useErrorBoundary:n,query:r})=>t.isError&&!e.isReset()&&!t.isFetching&&Eo(n,[t.error,r]))({result:c,errorResetBoundary:i.value,query:s.value.getCurrentQuery(),useErrorBoundary:a.value.useErrorBoundary})?{type:Mo.Error,data:c.error}:l.value?{type:Mo.Suspense,data:s.value.fetchOptimistic(a.value)}:{type:Mo.Success,data:c.data},d=go(()=>{const t=u();if(t.type===Mo.Success)return t.data;throw t.data});return Zi(()=>{if(l.value&&"load-on-access"!==o.value){const t=u();if(t.type===Mo.Suspense&&"suspend-eagerly"===o.value)throw t.data}}),go(()=>!(!l.value||"suspend-eagerly"!==o.value)&&u().type!==Mo.Success).value,c.dataSafe=void 0,en(()=>new Proxy(c,{get(t,e){return"data"===e?d.value:"dataSafe"===e?t.data:Reflect.get(...arguments)}}),[])});var Do;function Io(){}const qo=t=>{const e=Po(t),n=xo({context:go(()=>e.value.context).value}),r=go(()=>new ei(n.value,No(e.peek())));no(()=>{r.value.setOptions(No(e.value))});const i=en(()=>(t,e)=>{r.peek().mutate(t,e).catch(Io)},To),o=t=>({...t,mutate:i,mutateAsync:t.mutate}),a=Co(()=>({getCurrent:()=>o(r.value.getCurrentResult()),subscribe:t=>r.value.subscribe(e=>{t(o(e))})}));return go(()=>a.error&&Eo(r.value.options.useErrorBoundary,[a.error])).value&&Zi(()=>{throw a.error}),a},Lo=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Uo=t=>{const e=(t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase()))(t);return e.charAt(0).toUpperCase()+e.slice(1)},zo=(...t)=>t.filter((t,e,n)=>Boolean(t)&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim();
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
var Bo={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"};
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */const Wo=({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:r,children:i,iconNode:o,class:a="",...s})=>b("svg",{...Bo,width:String(e),height:e,stroke:t,"stroke-width":r?24*Number(n)/Number(e):n,class:["lucide",a].join(" "),...s},[...o.map(([t,e])=>b(t,e)),...R(i)]),Ho=(t,e)=>{const n=({class:n="",children:r,...i})=>b(Wo,{...i,iconNode:e,class:zo(`lucide-${Lo(Uo(t))}`,`lucide-${Lo(t)}`,n)},r);return n.displayName=Uo(t),n},$o=Ho("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),Qo=Ho("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),Vo=Ho("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Ko=Ho("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),Go=Ho("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),Jo=Ho("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Xo=Ho("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),Yo=Ho("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */!function(t){if("undefined"==typeof document)return;let e=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",e.firstChild?e.insertBefore(n,e.firstChild):e.appendChild(n),n.styleSheet?n.styleSheet.cssText=t:n.appendChild(document.createTextNode(t))}(':root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n');var Zo=t=>"number"==typeof t&&!isNaN(t),ta=t=>"string"==typeof t,ea=t=>"function"==typeof t,na=1,ra=()=>""+na++,ia=new Map,oa=[],aa=new Set,sa=()=>ia.size>0;function ca(t){if(sa()){if(null==t||(t=>ta(t)||Zo(t))(t))ia.forEach(e=>{e.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){let e=ia.get(t.containerId);e?e.removeToast(t.id):ia.forEach(e=>{e.removeToast(t.id)})}}else oa=oa.filter(e=>null!=t&&e.options.toastId!==t)}function la(t,e){(t=>Zn(t)||ta(t)||ea(t)||Zo(t))(t)&&(sa()||oa.push({content:t,options:e}),ia.forEach(n=>{n.buildToast(t,e)}))}function ua(t,e){ia.forEach(n=>{(null==e||null==e||!e.containerId||(null==e?void 0:e.containerId)===n.id)&&n.toggle(t,null==e?void 0:e.id)})}function da(t){return t&&(ta(t.toastId)||Zo(t.toastId))?t.toastId:ra()}function fa(t,e){return la(t,e),e.toastId}function ha(t,e){return{...e,type:e&&e.type||t,toastId:da(e)}}function pa(t){return(e,n)=>fa(e,ha(t,n))}function ma(t,e){return fa(t,ha("default",e))}function va({isOpen:t,onClose:e,watchlists:n,onAddToWatchlist:r,onCreateWatchlist:i,isLoading:o}){const[a,s]=Ge(""),[c,l]=Ge(!1),[u,d]=Ge("");if(!t)return null;const f=n.filter(t=>t.name.toLowerCase().includes(a.toLowerCase())),h=()=>{u.trim()?(i(u.trim()),d(""),l(!1)):ma.error("Watchlist name cannot be empty")},p=()=>{d(""),l(!1)};return $("div",{className:"modal-overlay",onClick:e,children:$("div",{className:"watchlist-modal",onClick:t=>t.stopPropagation(),children:[$("div",{className:"modal-header",children:$("div",{className:"modal-title",children:[$("h3",{children:"Add to Watchlist"}),$("button",{className:"modal-close",onClick:e,children:$(Yo,{size:20})})]})}),$("div",{className:"modal-content",children:[$("div",{className:"search-section",children:$("div",{className:"search-container",children:[$(Ko,{className:"search-icon",size:16}),$("input",{type:"text",placeholder:"Search watchlists...",value:a,onChange:t=>{s(t.currentTarget.value)},className:"search-input"})]})}),$("div",{className:"watchlists-section",children:[$("div",{className:"section-header",children:[$("h4",{children:"Select Watchlist"}),$("button",{className:"create-new-btn",onClick:()=>l(!0),disabled:c||o,children:[$(Vo,{size:14}),"New Watchlist"]})]}),$("div",{className:"watchlists-list",children:[c&&$("div",{className:"create-watchlist-form",children:[$("input",{type:"text",placeholder:"Watchlist name",value:u,onChange:t=>{d(t.currentTarget.value)},className:"create-input",onKeyDown:t=>{"Enter"===t.key&&h(),"Escape"===t.key&&p()},autoFocus:!0}),$("div",{className:"create-actions",children:[$("button",{className:"confirm-btn",onClick:h,disabled:o,children:$($o,{size:14})}),$("button",{className:"cancel-btn",onClick:p,disabled:o,children:$(Yo,{size:14})})]})]}),f.map(t=>$("div",{className:"watchlist-item",onClick:()=>r(t.id),children:[$("div",{className:"watchlist-info",children:[$("div",{className:"watchlist-name",children:[t.isDefault&&$(Go,{className:"default-star",size:14}),t.name]}),$("div",{className:"watchlist-count",children:[t.instrumentIds?.length||0," instruments"]})]}),$("button",{className:"add-btn",onClick:()=>r(t.id),disabled:o,children:o?$("div",{className:"loading-spinner small"}):$(Vo,{size:16})})]},t.id)),0===f.length&&a&&$("div",{className:"empty-state",children:$("p",{children:['No watchlists found matching "',a,'"']})})]})]})]})]})})}function ya(t,e){return function(){return t.apply(e,arguments)}}ma.loading=(t,e)=>fa(t,ha("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...e})),ma.promise=function(t,{pending:e,error:n,success:r},i){let o;e&&(o=ta(e)?ma.loading(e,i):ma.loading(e.render,{...i,...e}));let a={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},s=(t,e,n)=>{if(null==e)return void ma.dismiss(o);let r={type:t,...a,...i,data:n},s=ta(e)?{render:e}:e;return o?ma.update(o,{...r,...s}):ma(s.render,{...r,...s}),n},c=ea(t)?t():t;return c.then(t=>s("success",r,t)).catch(t=>s("error",n,t)),c},ma.success=pa("success"),ma.info=pa("info"),ma.error=pa("error"),ma.warning=pa("warning"),ma.warn=ma.warning,ma.dark=(t,e)=>fa(t,ha("default",{theme:"dark",...e})),ma.dismiss=function(t){ca(t)},ma.clearWaitingQueue=(t={})=>{ia.forEach(e=>{e.props.limit&&(!t.containerId||e.id===t.containerId)&&e.clearQueue()})},ma.isActive=function(t,e){var n;if(e)return!(null==(n=ia.get(e))||!n.isToastActive(t));let r=!1;return ia.forEach(e=>{e.isToastActive(t)&&(r=!0)}),r},ma.update=(t,e={})=>{let n=((t,{containerId:e})=>{var n;return null==(n=ia.get(e||1))?void 0:n.toasts.get(t)})(t,e);if(n){let{props:r,content:i}=n,o={delay:100,...r,...e,toastId:e.toastId||t,updateId:ra()};o.toastId!==t&&(o.staleId=t);let a=o.render||i;delete o.render,fa(a,o)}},ma.done=t=>{ma.update(t,{progress:1})},ma.onChange=function(t){return aa.add(t),()=>{aa.delete(t)}},ma.play=t=>ua(!0,t),ma.pause=t=>ua(!1,t);const{toString:ga}=Object.prototype,{getPrototypeOf:ba}=Object,{iterator:_a,toStringTag:wa}=Symbol,xa=(t=>e=>{const n=ga.call(e);return t[n]||(t[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),ka=t=>(t=t.toLowerCase(),e=>xa(e)===t),Sa=t=>e=>typeof e===t,{isArray:Oa}=Array,Ea=Sa("undefined");const Ca=ka("ArrayBuffer");const Ta=Sa("string"),Ra=Sa("function"),Aa=Sa("number"),Na=t=>null!==t&&"object"==typeof t,Pa=t=>{if("object"!==xa(t))return!1;const e=ba(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||wa in t||_a in t)},Ma=ka("Date"),Fa=ka("File"),ja=ka("Blob"),Da=ka("FileList"),Ia=ka("URLSearchParams"),[qa,La,Ua,za]=["ReadableStream","Request","Response","Headers"].map(ka);function Ba(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let r,i;if("object"!=typeof t&&(t=[t]),Oa(t))for(r=0,i=t.length;r<i;r++)e.call(null,t[r],r,t);else{const i=n?Object.getOwnPropertyNames(t):Object.keys(t),o=i.length;let a;for(r=0;r<o;r++)a=i[r],e.call(null,t[a],a,t)}}function Wa(t,e){e=e.toLowerCase();const n=Object.keys(t);let r,i=n.length;for(;i-- >0;)if(r=n[i],e===r.toLowerCase())return r;return null}const Ha="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,$a=t=>!Ea(t)&&t!==Ha;const Qa=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&ba(Uint8Array)),Va=ka("HTMLFormElement"),Ka=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),Ga=ka("RegExp"),Ja=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),r={};Ba(n,(n,i)=>{let o;!1!==(o=e(n,i,t))&&(r[i]=o||n)}),Object.defineProperties(t,r)};const Xa=ka("AsyncFunction"),Ya=(Za="function"==typeof setImmediate,ts=Ra(Ha.postMessage),Za?setImmediate:ts?(es=`axios@${Math.random()}`,ns=[],Ha.addEventListener("message",({source:t,data:e})=>{t===Ha&&e===es&&ns.length&&ns.shift()()},!1),t=>{ns.push(t),Ha.postMessage(es,"*")}):t=>setTimeout(t));var Za,ts,es,ns;const rs="undefined"!=typeof queueMicrotask?queueMicrotask.bind(Ha):"undefined"!=typeof process&&process.nextTick||Ya,is={isArray:Oa,isArrayBuffer:Ca,isBuffer:function(t){return null!==t&&!Ea(t)&&null!==t.constructor&&!Ea(t.constructor)&&Ra(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||Ra(t.append)&&("formdata"===(e=xa(t))||"object"===e&&Ra(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&Ca(t.buffer),e},isString:Ta,isNumber:Aa,isBoolean:t=>!0===t||!1===t,isObject:Na,isPlainObject:Pa,isReadableStream:qa,isRequest:La,isResponse:Ua,isHeaders:za,isUndefined:Ea,isDate:Ma,isFile:Fa,isBlob:ja,isRegExp:Ga,isFunction:Ra,isStream:t=>Na(t)&&Ra(t.pipe),isURLSearchParams:Ia,isTypedArray:Qa,isFileList:Da,forEach:Ba,merge:function t(){const{caseless:e}=$a(this)&&this||{},n={},r=(r,i)=>{const o=e&&Wa(n,i)||i;Pa(n[o])&&Pa(r)?n[o]=t(n[o],r):Pa(r)?n[o]=t({},r):Oa(r)?n[o]=r.slice():n[o]=r};for(let i=0,o=arguments.length;i<o;i++)arguments[i]&&Ba(arguments[i],r);return n},extend:(t,e,n,{allOwnKeys:r}={})=>(Ba(e,(e,r)=>{n&&Ra(e)?t[r]=ya(e,n):t[r]=e},{allOwnKeys:r}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,r)=>{t.prototype=Object.create(e.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,n,r)=>{let i,o,a;const s={};if(e=e||{},null==t)return e;do{for(i=Object.getOwnPropertyNames(t),o=i.length;o-- >0;)a=i[o],r&&!r(a,t,e)||s[a]||(e[a]=t[a],s[a]=!0);t=!1!==n&&ba(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},kindOf:xa,kindOfTest:ka,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const r=t.indexOf(e,n);return-1!==r&&r===n},toArray:t=>{if(!t)return null;if(Oa(t))return t;let e=t.length;if(!Aa(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[_a]).call(t);let r;for(;(r=n.next())&&!r.done;){const n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const r=[];for(;null!==(n=t.exec(e));)r.push(n);return r},isHTMLForm:Va,hasOwnProperty:Ka,hasOwnProp:Ka,reduceDescriptors:Ja,freezeMethods:t=>{Ja(t,(e,n)=>{if(Ra(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=t[n];Ra(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(t,e)=>{const n={},r=t=>{t.forEach(t=>{n[t]=!0})};return Oa(t)?r(t):r(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,e,n){return e.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:Wa,global:Ha,isContextDefined:$a,isSpecCompliantForm:function(t){return!!(t&&Ra(t.append)&&"FormData"===t[wa]&&t[_a])},toJSONObject:t=>{const e=new Array(10),n=(t,r)=>{if(Na(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[r]=t;const i=Oa(t)?[]:{};return Ba(t,(t,e)=>{const o=n(t,r+1);!Ea(o)&&(i[e]=o)}),e[r]=void 0,i}}return t};return n(t,0)},isAsyncFn:Xa,isThenable:t=>t&&(Na(t)||Ra(t))&&Ra(t.then)&&Ra(t.catch),setImmediate:Ya,asap:rs,isIterable:t=>null!=t&&Ra(t[_a])};function os(t,e,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}is.inherits(os,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:is.toJSONObject(this.config),code:this.code,status:this.status}}});const as=os.prototype,ss={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{ss[t]={value:t}}),Object.defineProperties(os,ss),Object.defineProperty(as,"isAxiosError",{value:!0}),os.from=(t,e,n,r,i,o)=>{const a=Object.create(as);return is.toFlatObject(t,a,function(t){return t!==Error.prototype},t=>"isAxiosError"!==t),os.call(a,t.message,e,n,r,i),a.cause=t,a.name=t.name,o&&Object.assign(a,o),a};function cs(t){return is.isPlainObject(t)||is.isArray(t)}function ls(t){return is.endsWith(t,"[]")?t.slice(0,-2):t}function us(t,e,n){return t?t.concat(e).map(function(t,e){return t=ls(t),!n&&e?"["+t+"]":t}).join(n?".":""):e}const ds=is.toFlatObject(is,{},null,function(t){return/^is[A-Z]/.test(t)});function fs(t,e,n){if(!is.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const r=(n=is.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(t,e){return!is.isUndefined(e[t])})).metaTokens,i=n.visitor||l,o=n.dots,a=n.indexes,s=(n.Blob||"undefined"!=typeof Blob&&Blob)&&is.isSpecCompliantForm(e);if(!is.isFunction(i))throw new TypeError("visitor must be a function");function c(t){if(null===t)return"";if(is.isDate(t))return t.toISOString();if(is.isBoolean(t))return t.toString();if(!s&&is.isBlob(t))throw new os("Blob is not supported. Use a Buffer instead.");return is.isArrayBuffer(t)||is.isTypedArray(t)?s&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function l(t,n,i){let s=t;if(t&&!i&&"object"==typeof t)if(is.endsWith(n,"{}"))n=r?n:n.slice(0,-2),t=JSON.stringify(t);else if(is.isArray(t)&&function(t){return is.isArray(t)&&!t.some(cs)}(t)||(is.isFileList(t)||is.endsWith(n,"[]"))&&(s=is.toArray(t)))return n=ls(n),s.forEach(function(t,r){!is.isUndefined(t)&&null!==t&&e.append(!0===a?us([n],r,o):null===a?n:n+"[]",c(t))}),!1;return!!cs(t)||(e.append(us(i,n,o),c(t)),!1)}const u=[],d=Object.assign(ds,{defaultVisitor:l,convertValue:c,isVisitable:cs});if(!is.isObject(t))throw new TypeError("data must be an object");return function t(n,r){if(!is.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),is.forEach(n,function(n,o){!0===(!(is.isUndefined(n)||null===n)&&i.call(e,n,is.isString(o)?o.trim():o,r,d))&&t(n,r?r.concat(o):[o])}),u.pop()}}(t),e}function hs(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(t){return e[t]})}function ps(t,e){this._pairs=[],t&&fs(t,this,e)}const ms=ps.prototype;function vs(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ys(t,e,n){if(!e)return t;const r=n&&n.encode||vs;is.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(o=i?i(e,n):is.isURLSearchParams(e)?e.toString():new ps(e,n).toString(r),o){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+o}return t}ms.append=function(t,e){this._pairs.push([t,e])},ms.toString=function(t){const e=t?function(e){return t.call(this,e,hs)}:hs;return this._pairs.map(function(t){return e(t[0])+"="+e(t[1])},"").join("&")};class gs{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){is.forEach(this.handlers,function(e){null!==e&&t(e)})}}const bs={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},_s={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:ps,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ws="undefined"!=typeof window&&"undefined"!=typeof document,xs="object"==typeof navigator&&navigator||void 0,ks=ws&&(!xs||["ReactNative","NativeScript","NS"].indexOf(xs.product)<0),Ss="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Os=ws&&window.location.href||"http://localhost",Es={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ws,hasStandardBrowserEnv:ks,hasStandardBrowserWebWorkerEnv:Ss,navigator:xs,origin:Os},Symbol.toStringTag,{value:"Module"})),..._s};function Cs(t){function e(t,n,r,i){let o=t[i++];if("__proto__"===o)return!0;const a=Number.isFinite(+o),s=i>=t.length;if(o=!o&&is.isArray(r)?r.length:o,s)return is.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!a;r[o]&&is.isObject(r[o])||(r[o]=[]);return e(t,n,r[o],i)&&is.isArray(r[o])&&(r[o]=function(t){const e={},n=Object.keys(t);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],e[o]=t[o];return e}(r[o])),!a}if(is.isFormData(t)&&is.isFunction(t.entries)){const n={};return is.forEachEntry(t,(t,r)=>{e(function(t){return is.matchAll(/\w+|\[(\w*)]/g,t).map(t=>"[]"===t[0]?"":t[1]||t[0])}(t),r,n,0)}),n}return null}const Ts={transitional:bs,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const n=e.getContentType()||"",r=n.indexOf("application/json")>-1,i=is.isObject(t);i&&is.isHTMLForm(t)&&(t=new FormData(t));if(is.isFormData(t))return r?JSON.stringify(Cs(t)):t;if(is.isArrayBuffer(t)||is.isBuffer(t)||is.isStream(t)||is.isFile(t)||is.isBlob(t)||is.isReadableStream(t))return t;if(is.isArrayBufferView(t))return t.buffer;if(is.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return fs(t,new Es.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,r){return Es.isNode&&is.isBuffer(t)?(this.append(e,t.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((o=is.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return fs(o?{"files[]":t}:t,e&&new e,this.formSerializer)}}return i||r?(e.setContentType("application/json",!1),function(t,e,n){if(is.isString(t))try{return(e||JSON.parse)(t),is.trim(t)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||Ts.transitional,n=e&&e.forcedJSONParsing,r="json"===this.responseType;if(is.isResponse(t)||is.isReadableStream(t))return t;if(t&&is.isString(t)&&(n&&!this.responseType||r)){const n=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(i){if(n){if("SyntaxError"===i.name)throw os.from(i,os.ERR_BAD_RESPONSE,this,null,this.response);throw i}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Es.classes.FormData,Blob:Es.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};is.forEach(["delete","get","head","post","put","patch"],t=>{Ts.headers[t]={}});const Rs=is.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),As=Symbol("internals");function Ns(t){return t&&String(t).trim().toLowerCase()}function Ps(t){return!1===t||null==t?t:is.isArray(t)?t.map(Ps):String(t)}function Ms(t,e,n,r,i){return is.isFunction(r)?r.call(this,e,n):(i&&(e=n),is.isString(e)?is.isString(r)?-1!==e.indexOf(r):is.isRegExp(r)?r.test(e):void 0:void 0)}let Fs=class{constructor(t){t&&this.set(t)}set(t,e,n){const r=this;function i(t,e,n){const i=Ns(e);if(!i)throw new Error("header name must be a non-empty string");const o=is.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||e]=Ps(t))}const o=(t,e)=>is.forEach(t,(t,n)=>i(t,n,e));if(is.isPlainObject(t)||t instanceof this.constructor)o(t,e);else if(is.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))o((t=>{const e={};let n,r,i;return t&&t.split("\n").forEach(function(t){i=t.indexOf(":"),n=t.substring(0,i).trim().toLowerCase(),r=t.substring(i+1).trim(),!n||e[n]&&Rs[n]||("set-cookie"===n?e[n]?e[n].push(r):e[n]=[r]:e[n]=e[n]?e[n]+", "+r:r)}),e})(t),e);else if(is.isObject(t)&&is.isIterable(t)){let n,r,i={};for(const e of t){if(!is.isArray(e))throw TypeError("Object iterator must return a key-value pair");i[r=e[0]]=(n=i[r])?is.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}o(i,e)}else null!=t&&i(e,t,n);return this}get(t,e){if(t=Ns(t)){const n=is.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(t);)e[r[1]]=r[2];return e}(t);if(is.isFunction(e))return e.call(this,t,n);if(is.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=Ns(t)){const n=is.findKey(this,t);return!(!n||void 0===this[n]||e&&!Ms(0,this[n],n,e))}return!1}delete(t,e){const n=this;let r=!1;function i(t){if(t=Ns(t)){const i=is.findKey(n,t);!i||e&&!Ms(0,n[i],i,e)||(delete n[i],r=!0)}}return is.isArray(t)?t.forEach(i):i(t),r}clear(t){const e=Object.keys(this);let n=e.length,r=!1;for(;n--;){const i=e[n];t&&!Ms(0,this[i],i,t,!0)||(delete this[i],r=!0)}return r}normalize(t){const e=this,n={};return is.forEach(this,(r,i)=>{const o=is.findKey(n,i);if(o)return e[o]=Ps(r),void delete e[i];const a=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,e,n)=>e.toUpperCase()+n)}(i):String(i).trim();a!==i&&delete e[i],e[a]=Ps(r),n[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return is.forEach(this,(n,r)=>{null!=n&&!1!==n&&(e[r]=t&&is.isArray(n)?n.join(", "):n)}),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,e])=>t+": "+e).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach(t=>n.set(t)),n}static accessor(t){const e=(this[As]=this[As]={accessors:{}}).accessors,n=this.prototype;function r(t){const r=Ns(t);e[r]||(!function(t,e){const n=is.toCamelCase(" "+e);["get","set","has"].forEach(r=>{Object.defineProperty(t,r+n,{value:function(t,n,i){return this[r].call(this,e,t,n,i)},configurable:!0})})}(n,t),e[r]=!0)}return is.isArray(t)?t.forEach(r):r(t),this}};function js(t,e){const n=this||Ts,r=e||n,i=Fs.from(r.headers);let o=r.data;return is.forEach(t,function(t){o=t.call(n,o,i.normalize(),e?e.status:void 0)}),i.normalize(),o}function Ds(t){return!(!t||!t.__CANCEL__)}function Is(t,e,n){os.call(this,null==t?"canceled":t,os.ERR_CANCELED,e,n),this.name="CanceledError"}function qs(t,e,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?e(new os("Request failed with status code "+n.status,[os.ERR_BAD_REQUEST,os.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}Fs.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),is.reduceDescriptors(Fs.prototype,({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}}),is.freezeMethods(Fs),is.inherits(Is,os,{__CANCEL__:!0});const Ls=(t,e,n=3)=>{let r=0;const i=function(t,e){t=t||10;const n=new Array(t),r=new Array(t);let i,o=0,a=0;return e=void 0!==e?e:1e3,function(s){const c=Date.now(),l=r[a];i||(i=c),n[o]=s,r[o]=c;let u=a,d=0;for(;u!==o;)d+=n[u++],u%=t;if(o=(o+1)%t,o===a&&(a=(a+1)%t),c-i<e)return;const f=l&&c-l;return f?Math.round(1e3*d/f):void 0}}(50,250);return function(t,e){let n,r,i=0,o=1e3/e;const a=(e,o=Date.now())=>{i=o,n=null,r&&(clearTimeout(r),r=null),t.apply(null,e)};return[(...t)=>{const e=Date.now(),s=e-i;s>=o?a(t,e):(n=t,r||(r=setTimeout(()=>{r=null,a(n)},o-s)))},()=>n&&a(n)]}(n=>{const o=n.loaded,a=n.lengthComputable?n.total:void 0,s=o-r,c=i(s);r=o;t({loaded:o,total:a,progress:a?o/a:void 0,bytes:s,rate:c||void 0,estimated:c&&a&&o<=a?(a-o)/c:void 0,event:n,lengthComputable:null!=a,[e?"download":"upload"]:!0})},n)},Us=(t,e)=>{const n=null!=t;return[r=>e[0]({lengthComputable:n,total:t,loaded:r}),e[1]]},zs=t=>(...e)=>is.asap(()=>t(...e)),Bs=Es.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,Es.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(Es.origin),Es.navigator&&/(msie|trident)/i.test(Es.navigator.userAgent)):()=>!0,Ws=Es.hasStandardBrowserEnv?{write(t,e,n,r,i,o){const a=[t+"="+encodeURIComponent(e)];is.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),is.isString(r)&&a.push("path="+r),is.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Hs(t,e,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(r||0==n)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const $s=t=>t instanceof Fs?{...t}:t;function Qs(t,e){e=e||{};const n={};function r(t,e,n,r){return is.isPlainObject(t)&&is.isPlainObject(e)?is.merge.call({caseless:r},t,e):is.isPlainObject(e)?is.merge({},e):is.isArray(e)?e.slice():e}function i(t,e,n,i){return is.isUndefined(e)?is.isUndefined(t)?void 0:r(void 0,t,0,i):r(t,e,0,i)}function o(t,e){if(!is.isUndefined(e))return r(void 0,e)}function a(t,e){return is.isUndefined(e)?is.isUndefined(t)?void 0:r(void 0,t):r(void 0,e)}function s(n,i,o){return o in e?r(n,i):o in t?r(void 0,n):void 0}const c={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s,headers:(t,e,n)=>i($s(t),$s(e),0,!0)};return is.forEach(Object.keys(Object.assign({},t,e)),function(r){const o=c[r]||i,a=o(t[r],e[r],r);is.isUndefined(a)&&o!==s||(n[r]=a)}),n}const Vs=t=>{const e=Qs({},t);let n,{data:r,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:a,headers:s,auth:c}=e;if(e.headers=s=Fs.from(s),e.url=ys(Hs(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&s.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),is.isFormData(r))if(Es.hasStandardBrowserEnv||Es.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[t,...e]=n?n.split(";").map(t=>t.trim()).filter(Boolean):[];s.setContentType([t||"multipart/form-data",...e].join("; "))}if(Es.hasStandardBrowserEnv&&(i&&is.isFunction(i)&&(i=i(e)),i||!1!==i&&Bs(e.url))){const t=o&&a&&Ws.read(a);t&&s.set(o,t)}return e},Ks="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise(function(e,n){const r=Vs(t);let i=r.data;const o=Fs.from(r.headers).normalize();let a,s,c,l,u,{responseType:d,onUploadProgress:f,onDownloadProgress:h}=r;function p(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(a),r.signal&&r.signal.removeEventListener("abort",a)}let m=new XMLHttpRequest;function v(){if(!m)return;const r=Fs.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());qs(function(t){e(t),p()},function(t){n(t),p()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:t,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=v:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(v)},m.onabort=function(){m&&(n(new os("Request aborted",os.ECONNABORTED,t,m)),m=null)},m.onerror=function(){n(new os("Network Error",os.ERR_NETWORK,t,m)),m=null},m.ontimeout=function(){let e=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||bs;r.timeoutErrorMessage&&(e=r.timeoutErrorMessage),n(new os(e,i.clarifyTimeoutError?os.ETIMEDOUT:os.ECONNABORTED,t,m)),m=null},void 0===i&&o.setContentType(null),"setRequestHeader"in m&&is.forEach(o.toJSON(),function(t,e){m.setRequestHeader(e,t)}),is.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),h&&([c,u]=Ls(h,!0),m.addEventListener("progress",c)),f&&m.upload&&([s,l]=Ls(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(a=e=>{m&&(n(!e||e.type?new Is(null,t,m):e),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(a),r.signal&&(r.signal.aborted?a():r.signal.addEventListener("abort",a)));const y=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(r.url);y&&-1===Es.protocols.indexOf(y)?n(new os("Unsupported protocol "+y+":",os.ERR_BAD_REQUEST,t)):m.send(i||null)})},Gs=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let n,r=new AbortController;const i=function(t){if(!n){n=!0,a();const e=t instanceof Error?t:this.reason;r.abort(e instanceof os?e:new Is(e instanceof Error?e.message:e))}};let o=e&&setTimeout(()=>{o=null,i(new os(`timeout ${e} of ms exceeded`,os.ETIMEDOUT))},e);const a=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach(t=>{t.unsubscribe?t.unsubscribe(i):t.removeEventListener("abort",i)}),t=null)};t.forEach(t=>t.addEventListener("abort",i));const{signal:s}=r;return s.unsubscribe=()=>is.asap(a),s}},Js=function*(t,e){let n=t.byteLength;if(n<e)return void(yield t);let r,i=0;for(;i<n;)r=i+e,yield t.slice(i,r),i=r},Xs=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);const e=t.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},Ys=(t,e,n,r)=>{const i=async function*(t,e){for await(const n of Xs(t))yield*Js(n,e)}(t,e);let o,a=0,s=t=>{o||(o=!0,r&&r(t))};return new ReadableStream({async pull(t){try{const{done:e,value:r}=await i.next();if(e)return s(),void t.close();let o=r.byteLength;if(n){let t=a+=o;n(t)}t.enqueue(new Uint8Array(r))}catch(e){throw s(e),e}},cancel:t=>(s(t),i.return())},{highWaterMark:2})},Zs="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tc=Zs&&"function"==typeof ReadableStream,ec=Zs&&("function"==typeof TextEncoder?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),nc=(t,...e)=>{try{return!!t(...e)}catch(n){return!1}},rc=tc&&nc(()=>{let t=!1;const e=new Request(Es.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),ic=tc&&nc(()=>is.isReadableStream(new Response("").body)),oc={stream:ic&&(t=>t.body)};var ac;Zs&&(ac=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!oc[t]&&(oc[t]=is.isFunction(ac[t])?e=>e[t]():(e,n)=>{throw new os(`Response type '${t}' is not supported`,os.ERR_NOT_SUPPORT,n)})}));const sc=async(t,e)=>{const n=is.toFiniteNumber(t.getContentLength());return null==n?(async t=>{if(null==t)return 0;if(is.isBlob(t))return t.size;if(is.isSpecCompliantForm(t)){const e=new Request(Es.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return is.isArrayBufferView(t)||is.isArrayBuffer(t)?t.byteLength:(is.isURLSearchParams(t)&&(t+=""),is.isString(t)?(await ec(t)).byteLength:void 0)})(e):n},cc={http:null,xhr:Ks,fetch:Zs&&(async t=>{let{url:e,method:n,data:r,signal:i,cancelToken:o,timeout:a,onDownloadProgress:s,onUploadProgress:c,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:f}=Vs(t);l=l?(l+"").toLowerCase():"text";let h,p=Gs([i,o&&o.toAbortSignal()],a);const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let v;try{if(c&&rc&&"get"!==n&&"head"!==n&&0!==(v=await sc(u,r))){let t,n=new Request(e,{method:"POST",body:r,duplex:"half"});if(is.isFormData(r)&&(t=n.headers.get("content-type"))&&u.setContentType(t),n.body){const[t,e]=Us(v,Ls(zs(c)));r=Ys(n.body,65536,t,e)}}is.isString(d)||(d=d?"include":"omit");const i="credentials"in Request.prototype;h=new Request(e,{...f,signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:i?d:void 0});let o=await fetch(h,f);const a=ic&&("stream"===l||"response"===l);if(ic&&(s||a&&m)){const t={};["status","statusText","headers"].forEach(e=>{t[e]=o[e]});const e=is.toFiniteNumber(o.headers.get("content-length")),[n,r]=s&&Us(e,Ls(zs(s),!0))||[];o=new Response(Ys(o.body,65536,n,()=>{r&&r(),m&&m()}),t)}l=l||"text";let y=await oc[is.findKey(oc,l)||"text"](o,t);return!a&&m&&m(),await new Promise((e,n)=>{qs(e,n,{data:y,headers:Fs.from(o.headers),status:o.status,statusText:o.statusText,config:t,request:h})})}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new os("Network Error",os.ERR_NETWORK,t,h),{cause:y.cause||y});throw os.from(y,y&&y.code,t,h)}})};is.forEach(cc,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(n){}Object.defineProperty(t,"adapterName",{value:e})}});const lc=t=>`- ${t}`,uc=t=>is.isFunction(t)||null===t||!1===t,dc=t=>{t=is.isArray(t)?t:[t];const{length:e}=t;let n,r;const i={};for(let o=0;o<e;o++){let e;if(n=t[o],r=n,!uc(n)&&(r=cc[(e=String(n)).toLowerCase()],void 0===r))throw new os(`Unknown adapter '${e}'`);if(r)break;i[e||"#"+o]=r}if(!r){const t=Object.entries(i).map(([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build"));throw new os("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(lc).join("\n"):" "+lc(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function fc(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Is(null,t)}function hc(t){fc(t),t.headers=Fs.from(t.headers),t.data=js.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);return dc(t.adapter||Ts.adapter)(t).then(function(e){return fc(t),e.data=js.call(t,t.transformResponse,e),e.headers=Fs.from(e.headers),e},function(e){return Ds(e)||(fc(t),e&&e.response&&(e.response.data=js.call(t,t.transformResponse,e.response),e.response.headers=Fs.from(e.response.headers))),Promise.reject(e)})}const pc="1.10.0",mc={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{mc[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const vc={};mc.transitional=function(t,e,n){function r(t,e){return"[Axios v"+pc+"] Transitional option '"+t+"'"+e+(n?". "+n:"")}return(n,i,o)=>{if(!1===t)throw new os(r(i," has been removed"+(e?" in "+e:"")),os.ERR_DEPRECATED);return e&&!vc[i]&&(vc[i]=!0,console.warn(r(i," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(n,i,o)}},mc.spelling=function(t){return(e,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};const yc={assertOptions:function(t,e,n){if("object"!=typeof t)throw new os("options must be an object",os.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let i=r.length;for(;i-- >0;){const o=r[i],a=e[o];if(a){const e=t[o],n=void 0===e||a(e,o,t);if(!0!==n)throw new os("option "+o+" must be "+n,os.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new os("Unknown option "+o,os.ERR_BAD_OPTION)}},validators:mc},gc=yc.validators;let bc=class{constructor(t){this.defaults=t||{},this.interceptors={request:new gs,response:new gs}}async request(t,e){try{return await this._request(t,e)}catch(n){if(n instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const e=t.stack?t.stack.replace(/^.+\n/,""):"";try{n.stack?e&&!String(n.stack).endsWith(e.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+e):n.stack=e}catch(r){}}throw n}}_request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Qs(this.defaults,e);const{transitional:n,paramsSerializer:r,headers:i}=e;void 0!==n&&yc.assertOptions(n,{silentJSONParsing:gc.transitional(gc.boolean),forcedJSONParsing:gc.transitional(gc.boolean),clarifyTimeoutError:gc.transitional(gc.boolean)},!1),null!=r&&(is.isFunction(r)?e.paramsSerializer={serialize:r}:yc.assertOptions(r,{encode:gc.function,serialize:gc.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),yc.assertOptions(e,{baseUrl:gc.spelling("baseURL"),withXsrfToken:gc.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let o=i&&is.merge(i.common,i[e.method]);i&&is.forEach(["delete","get","head","post","put","patch","common"],t=>{delete i[t]}),e.headers=Fs.concat(o,i);const a=[];let s=!0;this.interceptors.request.forEach(function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(s=s&&t.synchronous,a.unshift(t.fulfilled,t.rejected))});const c=[];let l;this.interceptors.response.forEach(function(t){c.push(t.fulfilled,t.rejected)});let u,d=0;if(!s){const t=[hc.bind(this),void 0];for(t.unshift.apply(t,a),t.push.apply(t,c),u=t.length,l=Promise.resolve(e);d<u;)l=l.then(t[d++],t[d++]);return l}u=a.length;let f=e;for(d=0;d<u;){const t=a[d++],e=a[d++];try{f=t(f)}catch(h){e.call(this,h);break}}try{l=hc.call(this,f)}catch(h){return Promise.reject(h)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(t){return ys(Hs((t=Qs(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}};is.forEach(["delete","get","head","options"],function(t){bc.prototype[t]=function(e,n){return this.request(Qs(n||{},{method:t,url:e,data:(n||{}).data}))}}),is.forEach(["post","put","patch"],function(t){function e(e){return function(n,r,i){return this.request(Qs(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}bc.prototype[t]=e(),bc.prototype[t+"Form"]=e(!0)});const _c={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(_c).forEach(([t,e])=>{_c[e]=t});const wc=function t(e){const n=new bc(e),r=ya(bc.prototype.request,n);return is.extend(r,bc.prototype,n,{allOwnKeys:!0}),is.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return t(Qs(e,n))},r}(Ts);wc.Axios=bc,wc.CanceledError=Is,wc.CancelToken=class t{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise(function(t){e=t});const n=this;this.promise.then(t=>{if(!n._listeners)return;let e=n._listeners.length;for(;e-- >0;)n._listeners[e](t);n._listeners=null}),this.promise.then=t=>{let e;const r=new Promise(t=>{n.subscribe(t),e=t}).then(t);return r.cancel=function(){n.unsubscribe(e)},r},t(function(t,r,i){n.reason||(n.reason=new Is(t,r,i),e(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){const t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let e;return{token:new t(function(t){e=t}),cancel:e}}},wc.isCancel=Ds,wc.VERSION=pc,wc.toFormData=fs,wc.AxiosError=os,wc.Cancel=wc.CanceledError,wc.all=function(t){return Promise.all(t)},wc.spread=function(t){return function(e){return t.apply(null,e)}},wc.isAxiosError=function(t){return is.isObject(t)&&!0===t.isAxiosError},wc.mergeConfig=Qs,wc.AxiosHeaders=Fs,wc.formToJSON=t=>Cs(is.isHTMLForm(t)?new FormData(t):t),wc.getAdapter=dc,wc.HttpStatusCode=_c,wc.default=wc;const{Axios:xc,AxiosError:kc,CanceledError:Sc,isCancel:Oc,CancelToken:Ec,VERSION:Cc,all:Tc,Cancel:Rc,isAxiosError:Ac,spread:Nc,toFormData:Pc,AxiosHeaders:Mc,HttpStatusCode:Fc,formToJSON:jc,getAdapter:Dc,mergeConfig:Ic}=wc;function qc(){return window.EurolandAppContext.command("authState").accessToken}const Lc=new class{baseUrl="http://localhost:5111";getHeaders(){return{Authorization:`Bearer ${qc()}`,"Content-Type":"application/json"}}handleError(t,e){if(wc.isAxiosError(t)){const n=t.response?.data?.message||`${e}: ${t.message}`;throw new Error(n)}throw t}async fetchWatchlists(){try{return(await wc.get(`${this.baseUrl}/api/watchlist/find-all`,{headers:this.getHeaders()})).data}catch(t){this.handleError(t,"Failed to fetch watchlists")}}async addInstrument(t,e){try{return(await wc.post(`${this.baseUrl}/api/WatchList/add-detail`,{instrumentIds:[t],watchlistId:e},{headers:this.getHeaders()})).data}catch(n){this.handleError(n,"Failed to add instrument")}}async removeInstrument(t,e){try{await wc.delete(`${this.baseUrl}/api/Watchlist/remove-detail`,{params:{WatchListId:t,Id:e.toString()},headers:{Authorization:`Bearer ${qc()}`}})}catch(n){this.handleError(n,"Failed to remove instrument")}}async createWatchlist(t){try{return(await wc.post(`${this.baseUrl}/api/WatchList/add`,{name:t},{headers:this.getHeaders()})).data}catch(e){this.handleError(e,"Failed to create watchlist")}}async updateWatchlist(t,e){try{return(await wc.put(`${this.baseUrl}/api/WatchList/update`,{name:e,id:t},{headers:this.getHeaders()})).data}catch(n){this.handleError(n,"Failed to update watchlist")}}async deleteWatchlist(t){try{await wc.delete(`${this.baseUrl}/api/WatchList/delete/${t}`,{headers:{Authorization:`Bearer ${qc()}`}})}catch(e){this.handleError(e,"Failed to delete watchlist")}}async getWatchlistById(t){try{return(await wc.get(`${this.baseUrl}/api/WatchList/find/${t}`,{headers:this.getHeaders()})).data}catch(e){this.handleError(e,"Failed to get watchlist by id")}}},Uc=()=>{const t=xo();return{watchlistsQuery:jo(()=>({queryKey:["watchlists"],queryFn:()=>Lc.fetchWatchlists(),staleTime:3e5})),createWatchlistMutation:qo(()=>({mutationFn:t=>Lc.createWatchlist(t),onSuccess:()=>{t.value.invalidateQueries({queryKey:["watchlists"]})}})),updateWatchlistMutation:qo(()=>({mutationFn:({id:t,name:e})=>Lc.updateWatchlist(t,e),onSuccess:()=>{t.value.invalidateQueries({queryKey:["watchlists"]})}})),deleteWatchlistMutation:qo(()=>({mutationFn:t=>Lc.deleteWatchlist(t),onSuccess:()=>{t.value.invalidateQueries({queryKey:["watchlists"]}),t.value.invalidateQueries({queryKey:["instruments"]})}})),addInstrumentMutation:qo(()=>({mutationFn:({instrumentId:t,watchlistId:e})=>Lc.addInstrument(t,e),onSuccess:()=>{t.value.invalidateQueries({queryKey:["instruments"]}),t.value.invalidateQueries({queryKey:["watchlists"]})}})),removeInstrumentMutation:qo(()=>({mutationFn:({watchlistId:t,instrumentId:e})=>Lc.removeInstrument(t,e),onSuccess:()=>{t.value.invalidateQueries({queryKey:["instruments"]}),t.value.invalidateQueries({queryKey:["watchlists"]})}}))}},zc=({instrumentId:t,onSuccess:e,onError:n,className:r=""})=>{const[i,o]=Ge(!1),[a,s]=Ge(!1),c=(()=>{const[t,e]=Ge(window.EurolandAppContext?.command("authState")||{});return Xe(()=>{const t=()=>{e(window.EurolandAppContext?.command("authState"))};return window.EurolandAppContext?.on("authChanged",t),()=>{window.EurolandAppContext?.off("authChanged",t)}},[]),t})(),{watchlistsQuery:l,addInstrumentMutation:u,createWatchlistMutation:d}=Uc();Xe(()=>{i&&l.refetch()},[i,l]);return c.isAuthenticated?$("div",{className:"add-instrument-container",children:[$("button",{className:`add-instrument-trigger ${r}`,onClick:()=>o(!0),title:"Add to watchlist",children:[$(Vo,{size:16}),"Add to watchlist"]}),$(va,{isOpen:i,onClose:()=>o(!1),watchlists:l.data?.data||[],instrumentId:t,onAddToWatchlist:async r=>{s(!0);try{await u.mutate({watchlistId:r,instrumentId:parseInt(t||"0")}),ma.success("Instrument added to watchlist");const n=l.data?.data?.find(t=>t.id===r);e?.(r,n?.name||""),l.refetch()}catch(i){n?.("Failed to add instrument to watchlist"),ma.error("Failed to add instrument"),console.error("Error adding instrument to watchlist:",i)}finally{s(!1)}},onCreateWatchlist:async t=>{s(!0);try{await d.mutate(t),ma.success("Watchlist created successfully"),o(!1),l.refetch()}catch(e){n?.("Failed to create new watchlist"),ma.error("Failed to create watchlist"),console.error("Error creating new watchlist:",e)}finally{s(!1)}},isLoading:a})]}):null},Bc=({onCreateWatchlist:t})=>{const[e,n]=Ge(!1),[r,i]=Ge(""),o=async()=>{if(r.trim())try{await t(r),i(""),n(!1)}catch(e){console.error("Error creating watchlist:",e),ma.error("Failed to create watchlist")}},a=()=>{n(!1),i("")};return $("div",{className:"watchlist-container",children:$("div",{className:"empty-watchlist-state",children:$("div",{className:"empty-watchlist-content",children:[$("div",{className:"empty-watchlist-icon",children:$(Xo,{size:64})}),$("h2",{className:"empty-watchlist-title",children:"No Watchlists Yet"}),$("p",{className:"empty-watchlist-description",children:"Create your first watchlist to start tracking your favorite instruments"}),$("button",{className:"create-first-watchlist-btn",onClick:()=>n(!0),children:[$(Vo,{size:16}),"Create Your First Watchlist"]}),e&&$("div",{className:"first-watchlist-form",children:[$("input",{type:"text",value:r,onChange:t=>i(t.currentTarget.value),placeholder:"Enter watchlist name",className:"first-watchlist-input",autoFocus:!0,onKeyDown:t=>{"Enter"===t.key&&o(),"Escape"===t.key&&a()}}),$("div",{className:"first-watchlist-actions",children:[$("button",{onClick:o,className:"confirm-first-btn",children:[$($o,{size:16}),"Create"]}),$("button",{onClick:a,className:"cancel-first-btn",children:[$(Yo,{size:16}),"Cancel"]})]})]})]})})})},Wc=({watchlists:t,activeWatchlistId:e,onWatchlistSelect:n,onCreateWatchlist:r})=>{const[i,o]=Ge(!1),[a,s]=Ge(""),[c,l]=Ge(""),[u,d]=Ge(""),[f,h]=Ge(""),{updateWatchlistMutation:p,deleteWatchlistMutation:m}=Uc(),v=Ze(null),y=Ze(null);Xe(()=>{i&&y.current&&v.current&&setTimeout(()=>{y.current?.scrollIntoView({behavior:"smooth",block:"nearest",inline:"end"})},50)},[i]);const g=async()=>{if(a.trim())try{o(!1),await r(a),s("")}catch(t){console.error("Error creating watchlist:",t),o(!0)}},b=async t=>{if(u.trim())try{p.mutate({id:t,name:u}),ma.success("Watchlist updated successfully"),l("")}catch(e){console.error("Error updating watchlist name:",e),ma.error("Failed to update watchlist")}else ma.error("Watchlist name cannot be empty")},_=(t,e)=>{"Enter"===t.key&&b(e),"Escape"===t.key&&(l(""),d(""))};return $("div",{className:"tab-bar",ref:v,children:$("div",{className:"tabs-container",children:[t.map(r=>$("div",{className:"tab "+(e===r.id?"active":""),onClick:()=>n(r.id),children:c===r.id?$("div",{className:"tab-edit-form",onClick:t=>t.stopPropagation(),children:$("input",{type:"text",value:u,onChange:t=>d(t.currentTarget.value),className:"tab-edit-input",autoFocus:!0,onKeyDown:t=>_(t,r.id),onBlur:()=>b(r.id)})}):$(w,{children:[$("div",{className:"tab-content",children:$("span",{className:"tab-name",children:r.name})}),$("div",{className:"tab-actions",children:[$("button",{className:"tab-action-btn edit-tab-btn",onClick:t=>{var e,n;t.stopPropagation(),e=r.id,n=r.name,l(e),d(n)},children:$(Qo,{size:14})}),f===r.id?$("div",{className:"delete-confirm",onClick:t=>t.stopPropagation(),children:[$("button",{className:"confirm-delete-btn",onClick:()=>(async t=>{try{m.mutate(t),ma.success("Watchlist deleted successfully"),h("")}catch(e){console.error("Error deleting watchlist:",e),ma.error("Failed to delete watchlist")}})(r.id),children:$($o,{size:16})}),$("button",{className:"cancel-delete-btn",onClick:()=>h(""),children:$(Yo,{size:16})})]}):$("button",{className:"tab-action-btn close-tab-btn",onClick:e=>{e.stopPropagation(),t.length<=1||h(r.id)},disabled:t.length<=1,children:$(Yo,{size:16})})]})]})},r.id)),i?$("div",{className:"add-tab-form",ref:y,children:[$("input",{type:"text",value:a,onChange:t=>s(t.currentTarget.value),placeholder:"Watchlist name",className:"add-tab-input",autoFocus:!0,onKeyDown:t=>{"Enter"===t.key&&g(),"Escape"===t.key&&(o(!1),s(""))}}),$("div",{className:"add-tab-actions",children:[$("button",{onClick:g,className:"confirm-add-btn",children:$($o,{size:16})}),$("button",{onClick:()=>{o(!1),s("")},className:"cancel-add-btn",children:$(Yo,{size:16})})]})]}):$("button",{className:"add-tab-btn",onClick:()=>{o(!0)},title:"Add new tab",children:$(Vo,{size:16})})]})})};function Hc(){return Hc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Hc.apply(null,arguments)}function $c(t){return($c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Qc(t){var e=function(t,e){if("object"!=$c(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e);if("object"!=$c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==$c(e)?e:e+""}function Vc(t,e,n){return(e=Qc(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Kc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function Gc(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Kc(Object(n),!0).forEach(function(e){Vc(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Kc(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Jc(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function Xc(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return Jc(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Jc(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Yc,Zc={exports:{}};
/*!
  	Copyright (c) 2018 Jed Watson.
  	Licensed under the MIT License (MIT), see
  	http://jedwatson.github.io/classnames
  */var tl,el=(Yc||(Yc=1,tl=Zc,function(){var t={}.hasOwnProperty;function e(){for(var t="",e=0;e<arguments.length;e++){var i=arguments[e];i&&(t=r(t,n(i)))}return t}function n(n){if("string"==typeof n||"number"==typeof n)return n;if("object"!=typeof n)return"";if(Array.isArray(n))return e.apply(null,n);if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]"))return n.toString();var i="";for(var o in n)t.call(n,o)&&n[o]&&(i=r(i,o));return i}function r(t,e){return e?t?t+" "+e:t+e:t}tl.exports?(e.default=e,tl.exports=e):window.classNames=e}()),Zc.exports);const nl=ni(el);var rl=Symbol.for("react.element"),il=Symbol.for("react.transitional.element"),ol=Symbol.for("react.fragment");function al(t){return t&&"object"===$c(t)&&(t.$$typeof===rl||t.$$typeof===il)&&t.type===ol}function sl(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[];return nr.Children.forEach(t,function(t){(null!=t||e.keepEmpty)&&(Array.isArray(t)?n=n.concat(sl(t)):al(t)&&t.props?n=n.concat(sl(t.props.children,e)):n.push(t))}),n}function cl(t){return t instanceof HTMLElement||t instanceof SVGElement}function ll(t){var e,n=function(t){return t&&"object"===$c(t)&&cl(t.nativeElement)?t.nativeElement:cl(t)?t:null}(t);return n||(t instanceof nr.Component?null===(e=nr.findDOMNode)||void 0===e?void 0:e.call(nr,t):null)}var ul,dl,fl={exports:{}},hl={};var pl=(dl||(dl=1,fl.exports=function(){if(ul)return hl;ul=1;var t,e=Symbol.for("react.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen");function m(t){if("object"==typeof t&&null!==t){var p=t.$$typeof;switch(p){case e:switch(t=t.type){case r:case o:case i:case u:case d:return t;default:switch(t=t&&t.$$typeof){case c:case s:case l:case h:case f:case a:return t;default:return p}}case n:return p}}}return t=Symbol.for("react.module.reference"),hl.ContextConsumer=s,hl.ContextProvider=a,hl.Element=e,hl.ForwardRef=l,hl.Fragment=r,hl.Lazy=h,hl.Memo=f,hl.Portal=n,hl.Profiler=o,hl.StrictMode=i,hl.Suspense=u,hl.SuspenseList=d,hl.isAsyncMode=function(){return!1},hl.isConcurrentMode=function(){return!1},hl.isContextConsumer=function(t){return m(t)===s},hl.isContextProvider=function(t){return m(t)===a},hl.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},hl.isForwardRef=function(t){return m(t)===l},hl.isFragment=function(t){return m(t)===r},hl.isLazy=function(t){return m(t)===h},hl.isMemo=function(t){return m(t)===f},hl.isPortal=function(t){return m(t)===n},hl.isProfiler=function(t){return m(t)===o},hl.isStrictMode=function(t){return m(t)===i},hl.isSuspense=function(t){return m(t)===u},hl.isSuspenseList=function(t){return m(t)===d},hl.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===o||e===i||e===u||e===d||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===f||e.$$typeof===a||e.$$typeof===s||e.$$typeof===l||e.$$typeof===t||void 0!==e.getModuleId)},hl.typeOf=m,hl}()),fl.exports);var ml=Number("18.3.1".split(".")[0]),vl=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e.filter(Boolean);return r.length<=1?r[0]:function(t){e.forEach(function(e){!function(t,e){"function"==typeof t?t(e):"object"===$c(t)&&t&&"current"in t&&(t.current=e)}(e,t)})}},yl=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return r=function(){return vl.apply(void 0,e)},i=e,o=function(t,e){return t.length!==e.length||t.every(function(t,n){return t!==e[n]})},"value"in(a=Ze({})).current&&!o(a.current.condition,i)||(a.current.value=r(),a.current.condition=i),a.current.value;var r,i,o,a};function gl(t){return Zn(t)&&!al(t)}var bl=W(null);var _l=function(){if("undefined"!=typeof Map)return Map;function t(t,e){var n=-1;return t.some(function(t,r){return t[0]===e&&(n=r,!0)}),n}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var n=t(this.__entries__,e),r=this.__entries__[n];return r&&r[1]},e.prototype.set=function(e,n){var r=t(this.__entries__,e);~r?this.__entries__[r][1]=n:this.__entries__.push([e,n])},e.prototype.delete=function(e){var n=this.__entries__,r=t(n,e);~r&&n.splice(r,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];t.call(e,i[1],i[0])}},e}()}(),wl="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,xl="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),kl="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(xl):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)};var Sl=["top","right","bottom","left","width","height","size","weight"],Ol="undefined"!=typeof MutationObserver,El=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(t,e){var n=!1,r=!1,i=0;function o(){n&&(n=!1,t()),r&&s()}function a(){kl(o)}function s(){var t=Date.now();if(n){if(t-i<2)return;r=!0}else n=!0,r=!1,setTimeout(a,e);i=t}return s}(this.refresh.bind(this),20)}return t.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},t.prototype.removeObserver=function(t){var e=this.observers_,n=e.indexOf(t);~n&&e.splice(n,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},t.prototype.updateObservers_=function(){var t=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return t.forEach(function(t){return t.broadcastActive()}),t.length>0},t.prototype.connect_=function(){wl&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Ol?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){wl&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(t){var e=t.propertyName,n=void 0===e?"":e;Sl.some(function(t){return!!~n.indexOf(t)})&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),Cl=function(t,e){for(var n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];Object.defineProperty(t,i,{value:e[i],enumerable:!1,writable:!1,configurable:!0})}return t},Tl=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||xl},Rl=jl(0,0,0,0);function Al(t){return parseFloat(t)||0}function Nl(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.reduce(function(e,n){return e+Al(t["border-"+n+"-width"])},0)}function Pl(t){var e=t.clientWidth,n=t.clientHeight;if(!e&&!n)return Rl;var r=Tl(t).getComputedStyle(t),i=function(t){for(var e={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var i=r[n],o=t["padding-"+i];e[i]=Al(o)}return e}(r),o=i.left+i.right,a=i.top+i.bottom,s=Al(r.width),c=Al(r.height);if("border-box"===r.boxSizing&&(Math.round(s+o)!==e&&(s-=Nl(r,"left","right")+o),Math.round(c+a)!==n&&(c-=Nl(r,"top","bottom")+a)),!function(t){return t===Tl(t).document.documentElement}(t)){var l=Math.round(s+o)-e,u=Math.round(c+a)-n;1!==Math.abs(l)&&(s-=l),1!==Math.abs(u)&&(c-=u)}return jl(i.left,i.top,s,c)}var Ml="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof Tl(t).SVGGraphicsElement}:function(t){return t instanceof Tl(t).SVGElement&&"function"==typeof t.getBBox};function Fl(t){return wl?Ml(t)?function(t){var e=t.getBBox();return jl(0,0,e.width,e.height)}(t):Pl(t):Rl}function jl(t,e,n,r){return{x:t,y:e,width:n,height:r}}var Dl=function(){function t(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=jl(0,0,0,0),this.target=t}return t.prototype.isActive=function(){var t=Fl(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},t}(),Il=function(){return function(t,e){var n,r,i,o,a,s,c,l=(r=(n=e).x,i=n.y,o=n.width,a=n.height,s="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,c=Object.create(s.prototype),Cl(c,{x:r,y:i,width:o,height:a,top:i,right:r+o,bottom:a+i,left:r}),c);Cl(this,{target:t,contentRect:l})}}(),ql=function(){function t(t,e,n){if(this.activeObservations_=[],this.observations_=new _l,"function"!=typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=n}return t.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof Tl(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new Dl(t)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof Tl(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},t.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map(function(t){return new Il(t.target,t.broadcastRect())});this.callback_.call(t,e,t),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),Ll="undefined"!=typeof WeakMap?new WeakMap:new _l,Ul=function(){return function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=El.getInstance(),r=new ql(e,n,this);Ll.set(this,r)}}();["observe","unobserve","disconnect"].forEach(function(t){Ul.prototype[t]=function(){var e;return(e=Ll.get(this))[t].apply(e,arguments)}});var zl=void 0!==xl.ResizeObserver?xl.ResizeObserver:Ul,Bl=new Map;var Wl=new zl(function(t){t.forEach(function(t){var e,n=t.target;null===(e=Bl.get(n))||void 0===e||e.forEach(function(t){return t(n)})})});function Hl(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function $l(t,e,n){return e&&function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Qc(r.key),r)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ql(t,e){return(Ql=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Vl(t){return(Vl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Kl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Kl=function(){return!!t})()}function Gl(t,e){if(e&&("object"==$c(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}var Jl=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ql(t,e)}(i,t);var e,n,r=(e=i,n=Kl(),function(){var t,r=Vl(e);if(n){var i=Vl(this).constructor;t=Reflect.construct(r,arguments,i)}else t=r.apply(this,arguments);return Gl(this,t)});function i(){return Hl(this,i),r.apply(this,arguments)}return $l(i,[{key:"render",value:function(){return this.props.children}}]),i}(x);function Xl(t,e){var n=t.children,r=t.disabled,i=Ze(null),o=Ze(null),a=rn(bl),s="function"==typeof n,c=s?n(i):n,l=Ze({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),u=!s&&Zn(c)&&function(t){var e,n;if(!t)return!1;if(gl(t)&&ml>=19)return!0;var r=pl.isMemo(t)?t.type.type:t.type;return!!("function"!=typeof r||null!==(e=r.prototype)&&void 0!==e&&e.render||r.$$typeof===pl.ForwardRef)&&!!("function"!=typeof t||null!==(n=t.prototype)&&void 0!==n&&n.render||t.$$typeof===pl.ForwardRef)}(c),d=u?function(t){if(t&&gl(t)){var e=t;return e.props.propertyIsEnumerable("ref")?e.props.ref:e.ref}return null}(c):null,f=yl(d,i),h=function(){var t;return ll(i.current)||(i.current&&"object"===$c(i.current)?ll(null===(t=i.current)||void 0===t?void 0:t.nativeElement):null)||ll(o.current)};tn(e,function(){return h()});var p=Ze(t);p.current=t;var m=nn(function(t){var e=p.current,n=e.onResize,r=e.data,i=t.getBoundingClientRect(),o=i.width,s=i.height,c=t.offsetWidth,u=t.offsetHeight,d=Math.floor(o),f=Math.floor(s);if(l.current.width!==d||l.current.height!==f||l.current.offsetWidth!==c||l.current.offsetHeight!==u){var h={width:d,height:f,offsetWidth:c,offsetHeight:u};l.current=h;var m=c===Math.round(o)?o:c,v=u===Math.round(s)?s:u,y=Gc(Gc({},h),{},{offsetWidth:m,offsetHeight:v});null==a||a(y,t,r),n&&Promise.resolve().then(function(){n(y,t)})}},[]);return Xe(function(){var t,e,n=h();return n&&!r&&(t=n,e=m,Bl.has(t)||(Bl.set(t,new Set),Wl.observe(t)),Bl.get(t).add(e)),function(){return function(t,e){Bl.has(t)&&(Bl.get(t).delete(e),Bl.get(t).size||(Wl.unobserve(t),Bl.delete(t)))}(n,m)}},[i.current,r]),b(Jl,{ref:o},u?tr(c,{ref:f}):c)}var Yl=On(Xl);function Zl(t,e){var n=t.children;return("function"==typeof n?[n]:sl(n)).map(function(n,r){var i=(null==n?void 0:n.key)||"".concat("rc-observer-key","-").concat(r);return b(Yl,Hc({},t,{key:i,ref:0===r?e:void 0}),n)})}var tu=On(Zl);function eu(t){var e=Ze();e.current=t;var n=nn(function(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return null===(t=e.current)||void 0===t?void 0:t.call.apply(t,[e].concat(r))},[]);return n}tu.Collection=function(t){var e=t.children,n=t.onBatchResize,r=Ze(0),i=Ze([]),o=rn(bl),a=nn(function(t,e,a){r.current+=1;var s=r.current;i.current.push({size:t,element:e,data:a}),Promise.resolve().then(function(){s===r.current&&(null==n||n(i.current),i.current=[])}),null==o||o(t,e,a)},[n,o]);return b(bl.Provider,{value:a},e)};var nu="undefined"!=typeof window&&window.document&&window.document.createElement?Ye:Xe,ru=function(t,e){var n=Ze(!0);nu(function(){return t(n.current)},e),nu(function(){return n.current=!1,function(){n.current=!0}},[])},iu=On(function(t,e){var n=t.height,r=t.offsetY,i=t.offsetX,o=t.children,a=t.prefixCls,s=t.onInnerResize,c=t.innerProps,l=t.rtl,u=t.extra,d={},f={display:"flex",flexDirection:"column"};return void 0!==r&&(d={height:n,position:"relative",overflow:"hidden"},f=Gc(Gc({},f),{},Vc(Vc(Vc(Vc(Vc({transform:"translateY(".concat(r,"px)")},l?"marginRight":"marginLeft",-i),"position","absolute"),"left",0),"right",0),"top",0))),b("div",{style:d},b(tu,{onResize:function(t){t.offsetHeight&&s&&s()}},b("div",Hc({style:f,className:nl(Vc({},"".concat(a,"-holder-inner"),a)),ref:e},c),o,u)))});function ou(t){var e=t.children,n=t.setRef;return tr(e,{ref:nn(function(t){n(t)},[])})}function au(t,e,n){var r=Xc(Ge(t),2),i=r[0],o=r[1],a=Xc(Ge(null),2),s=a[0],c=a[1];return Xe(function(){var n=function(t,e,n){var r,i,o=t.length,a=e.length;if(0===o&&0===a)return null;o<a?(r=t,i=e):(r=e,i=t);var s={__EMPTY_ITEM__:!0};function c(t){return void 0!==t?n(t):s}for(var l=null,u=1!==Math.abs(o-a),d=0;d<i.length;d+=1){var f=c(r[d]);if(f!==c(i[d])){l=d,u=u||f!==c(i[d+1]);break}}return null===l?null:{index:l,multiple:u}}(i||[],t||[],e);void 0!==(null==n?void 0:n.index)&&c(t[n.index]),o(t)},[t]),[s]}iu.displayName="Filler";var su=function(t){return+setTimeout(t,16)},cu=function(t){return clearTimeout(t)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(su=function(t){return window.requestAnimationFrame(t)},cu=function(t){return window.cancelAnimationFrame(t)});var lu=0,uu=new Map;function du(t){uu.delete(t)}var fu=function(t){var e=lu+=1;return function n(r){if(0===r)du(e),t();else{var i=su(function(){n(r-1)});uu.set(e,i)}}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:1),e};fu.cancel=function(t){var e=uu.get(t);return du(t),cu(e)};var hu="object"===("undefined"==typeof navigator?"undefined":$c(navigator))&&/Firefox/i.test(navigator.userAgent);const pu=function(t,e,n,r){var i=Ze(!1),o=Ze(null);var a=Ze({top:t,bottom:e,left:n,right:r});return a.current.top=t,a.current.bottom=e,a.current.left=n,a.current.right=r,function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t?e<0&&a.current.left||e>0&&a.current.right:e<0&&a.current.top||e>0&&a.current.bottom;return n&&r?(clearTimeout(o.current),i.current=!1):r&&!i.current||(clearTimeout(o.current),i.current=!0,o.current=setTimeout(function(){i.current=!1},50)),!i.current&&r}};function mu(t,e,n,r,i,o,a){var s=Ze(0),c=Ze(null),l=Ze(null),u=Ze(!1),d=pu(e,n,r,i);var f=Ze(null),h=Ze(null);return[function(e){if(t){fu.cancel(h.current),h.current=fu(function(){f.current=null},2);var n=e.deltaX,r=e.deltaY,i=e.shiftKey,p=n,m=r;("sx"===f.current||!f.current&&i&&r&&!n)&&(p=r,m=0,f.current="sx");var v=Math.abs(p),y=Math.abs(m);null===f.current&&(f.current=o&&v>y?"x":"y"),"y"===f.current?function(t,e){if(fu.cancel(c.current),!d(!1,e)){var n=t;n._virtualHandled||(n._virtualHandled=!0,s.current+=e,l.current=e,hu||n.preventDefault(),c.current=fu(function(){var t=u.current?10:1;a(s.current*t,!1),s.current=0}))}}(e,m):function(t,e){a(e,!0),hu||t.preventDefault()}(e,p)}},function(e){t&&(u.current=e.detail===l.current)}]}var vu=function(){function t(){Hl(this,t),Vc(this,"maps",void 0),Vc(this,"id",0),Vc(this,"diffRecords",new Map),this.maps=Object.create(null)}return $l(t,[{key:"set",value:function(t,e){this.diffRecords.set(t,this.maps[t]),this.maps[t]=e,this.id+=1}},{key:"get",value:function(t){return this.maps[t]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),t}();function yu(t){var e=parseFloat(t);return isNaN(e)?0:e}var gu=14/15;function bu(t){return Math.floor(Math.pow(t,.5))}function _u(t,e){return("touches"in t?t.touches[0]:t)[e?"pageX":"pageY"]-window[e?"scrollX":"scrollY"]}var wu=On(function(t,e){var n=t.prefixCls,r=t.rtl,i=t.scrollOffset,o=t.scrollRange,a=t.onStartMove,s=t.onStopMove,c=t.onScroll,l=t.horizontal,u=t.spinSize,d=t.containerSize,f=t.style,h=t.thumbStyle,p=t.showScrollBar,m=Xc(Ge(!1),2),v=m[0],y=m[1],g=Xc(Ge(null),2),_=g[0],w=g[1],x=Xc(Ge(null),2),k=x[0],S=x[1],O=!r,E=Ze(),C=Ze(),T=Xc(Ge(p),2),R=T[0],A=T[1],N=Ze(),P=function(){!0!==p&&!1!==p&&(clearTimeout(N.current),A(!0),N.current=setTimeout(function(){A(!1)},3e3))},M=o-d||0,F=d-u||0,j=en(function(){return 0===i||0===M?0:i/M*F},[i,M,F]),D=Ze({top:j,dragging:v,pageY:_,startTop:k});D.current={top:j,dragging:v,pageY:_,startTop:k};var I=function(t){y(!0),w(_u(t,l)),S(D.current.top),a(),t.stopPropagation(),t.preventDefault()};Xe(function(){var t=function(t){t.preventDefault()},e=E.current,n=C.current;return e.addEventListener("touchstart",t,{passive:!1}),n.addEventListener("touchstart",I,{passive:!1}),function(){e.removeEventListener("touchstart",t),n.removeEventListener("touchstart",I)}},[]);var q=Ze();q.current=M;var L=Ze();L.current=F,Xe(function(){if(v){var t,e=function(e){var n=D.current,r=n.dragging,i=n.pageY,o=n.startTop;fu.cancel(t);var a=E.current.getBoundingClientRect(),s=d/(l?a.width:a.height);if(r){var u=(_u(e,l)-i)*s,f=o;!O&&l?f-=u:f+=u;var h=q.current,p=L.current,m=p?f/p:0,v=Math.ceil(m*h);v=Math.max(v,0),v=Math.min(v,h),t=fu(function(){c(v,l)})}},n=function(){y(!1),s()};return window.addEventListener("mousemove",e,{passive:!0}),window.addEventListener("touchmove",e,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",e),window.removeEventListener("touchmove",e),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),fu.cancel(t)}}},[v]),Xe(function(){return P(),function(){clearTimeout(N.current)}},[i]),tn(e,function(){return{delayHidden:P}});var U="".concat(n,"-scrollbar"),z={position:"absolute",visibility:R?null:"hidden"},B={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return l?(Object.assign(z,{height:8,left:0,right:0,bottom:0}),Object.assign(B,Vc({height:"100%",width:u},O?"left":"right",j))):(Object.assign(z,Vc({width:8,top:0,bottom:0},O?"right":"left",0)),Object.assign(B,{width:"100%",height:u,top:j})),b("div",{ref:E,className:nl(U,Vc(Vc(Vc({},"".concat(U,"-horizontal"),l),"".concat(U,"-vertical"),!l),"".concat(U,"-visible"),R)),style:Gc(Gc({},z),f),onMouseDown:function(t){t.stopPropagation(),t.preventDefault()},onMouseMove:P},b("div",{ref:C,className:nl("".concat(U,"-thumb"),Vc({},"".concat(U,"-thumb-moving"),v)),style:Gc(Gc({},B),h),onMouseDown:I}))});function xu(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=t/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*t;return isNaN(e)&&(e=0),e=Math.max(e,20),Math.floor(e)}var ku=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Su=[],Ou={overflowY:"auto",overflowAnchor:"none"};function Eu(t,e){var n=t.prefixCls,r=void 0===n?"rc-virtual-list":n,i=t.className,o=t.height,a=t.itemHeight,s=t.fullHeight,c=void 0===s||s,l=t.style,u=t.data,d=t.children,f=t.itemKey,h=t.virtual,p=t.direction,m=t.scrollWidth,v=t.component,y=void 0===v?"div":v,g=t.onScroll,_=t.onVirtualScroll,w=t.onVisibleChange,x=t.innerProps,k=t.extraRender,S=t.styles,O=t.showScrollBar,E=void 0===O?"optional":O,C=function(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],-1===e.indexOf(n)&&{}.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(t,ku),T=nn(function(t){return"function"==typeof f?f(t):null==t?void 0:t[f]},[f]),R=function(t){var e=Xc(Ge(0),2),n=e[0],r=e[1],i=Ze(new Map),o=Ze(new vu),a=Ze(0);function s(){a.current+=1}function c(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];s();var e=function(){var t=!1;i.current.forEach(function(e,n){if(e&&e.offsetParent){var r=e.offsetHeight,i=getComputedStyle(e),a=i.marginTop,s=i.marginBottom,c=r+yu(a)+yu(s);o.current.get(n)!==c&&(o.current.set(n,c),t=!0)}}),t&&r(function(t){return t+1})};if(t)e();else{a.current+=1;var n=a.current;Promise.resolve().then(function(){n===a.current&&e()})}}return Xe(function(){return s},[]),[function(e,n){var r=t(e);i.current.get(r),n?(i.current.set(r,n),c()):i.current.delete(r)},c,o.current,n]}(T),A=Xc(R,4),N=A[0],P=A[1],M=A[2],F=A[3],j=!(!1===h||!o||!a),D=en(function(){return Object.values(M.maps).reduce(function(t,e){return t+e},0)},[M.id,M.maps]),I=j&&u&&(Math.max(a*u.length,D)>o||!!m),q="rtl"===p,L=nl(r,Vc({},"".concat(r,"-rtl"),q),i),U=u||Su,z=Ze(),B=Ze(),W=Ze(),H=Xc(Ge(0),2),$=H[0],Q=H[1],V=Xc(Ge(0),2),K=V[0],G=V[1],J=Xc(Ge(!1),2),X=J[0],Y=J[1],Z=function(){Y(!0)},tt=function(){Y(!1)},et={getKey:T};function nt(t){Q(function(e){var n=function(t){var e=t;Number.isNaN(bt.current)||(e=Math.min(e,bt.current));return e=Math.max(e,0),e}("function"==typeof t?t(e):t);return z.current.scrollTop=n,n})}var rt=Ze({start:0,end:U.length}),it=Ze(),ot=Xc(au(U,T),1)[0];it.current=ot;var at=en(function(){if(!j)return{scrollHeight:void 0,start:0,end:U.length-1,offset:void 0};var t;if(!I)return{scrollHeight:(null===(t=B.current)||void 0===t?void 0:t.offsetHeight)||0,start:0,end:U.length-1,offset:void 0};for(var e,n,r,i=0,s=U.length,c=0;c<s;c+=1){var l=U[c],u=T(l),d=M.get(u),f=i+(void 0===d?a:d);f>=$&&void 0===e&&(e=c,n=i),f>$+o&&void 0===r&&(r=c),i=f}return void 0===e&&(e=0,n=0,r=Math.ceil(o/a)),void 0===r&&(r=U.length-1),{scrollHeight:i,start:e,end:r=Math.min(r+1,U.length-1),offset:n}},[I,j,$,U,F,o]),st=at.scrollHeight,ct=at.start,lt=at.end,ut=at.offset;rt.current.start=ct,rt.current.end=lt,Ye(function(){var t=M.getRecord();if(1===t.size){var e=Array.from(t.keys())[0],n=t.get(e),r=U[ct];if(r&&void 0===n)if(T(r)===e){var i=M.get(e)-a;nt(function(t){return t+i})}}M.resetRecord()},[st]);var dt=Xc(Ge({width:0,height:o}),2),ft=dt[0],ht=dt[1],pt=Ze(),mt=Ze(),vt=en(function(){return xu(ft.width,m)},[ft.width,m]),yt=en(function(){return xu(ft.height,st)},[ft.height,st]),gt=st-o,bt=Ze(gt);bt.current=gt;var _t=$<=0,wt=$>=gt,xt=K<=0,kt=K>=m,St=pu(_t,wt,xt,kt),Ot=function(){return{x:q?-K:K,y:$}},Et=Ze(Ot()),Ct=eu(function(t){if(_){var e=Gc(Gc({},Ot()),t);Et.current.x===e.x&&Et.current.y===e.y||(_(e),Et.current=e)}});function Tt(t,e){var n=t;e?(er(function(){G(n)}),Ct()):nt(n)}var Rt=function(t){var e=t,n=m?m-ft.width:0;return e=Math.max(e,0),e=Math.min(e,n)},At=eu(function(t,e){e?(er(function(){G(function(e){return Rt(e+(q?-t:t))})}),Ct()):nt(function(e){return e+t})}),Nt=Xc(mu(j,_t,wt,xt,kt,!!m,At),2),Pt=Nt[0],Mt=Nt[1];!function(t,e,n){var r,i=Ze(!1),o=Ze(0),a=Ze(0),s=Ze(null),c=Ze(null),l=function(t){if(i.current){var e=Math.ceil(t.touches[0].pageX),r=Math.ceil(t.touches[0].pageY),s=o.current-e,l=a.current-r,u=Math.abs(s)>Math.abs(l);u?o.current=e:a.current=r;var d=n(u,u?s:l,!1,t);d&&t.preventDefault(),clearInterval(c.current),d&&(c.current=setInterval(function(){u?s*=gu:l*=gu;var t=Math.floor(u?s:l);(!n(u,t,!0)||Math.abs(t)<=.1)&&clearInterval(c.current)},16))}},u=function(){i.current=!1,r()},d=function(t){r(),1!==t.touches.length||i.current||(i.current=!0,o.current=Math.ceil(t.touches[0].pageX),a.current=Math.ceil(t.touches[0].pageY),s.current=t.target,s.current.addEventListener("touchmove",l,{passive:!1}),s.current.addEventListener("touchend",u,{passive:!0}))};r=function(){s.current&&(s.current.removeEventListener("touchmove",l),s.current.removeEventListener("touchend",u))},ru(function(){return t&&e.current.addEventListener("touchstart",d,{passive:!0}),function(){var t;null===(t=e.current)||void 0===t||t.removeEventListener("touchstart",d),r(),clearInterval(c.current)}},[t])}(j,z,function(t,e,n,r){var i=r;return!St(t,e,n)&&((!i||!i._virtualHandled)&&(i&&(i._virtualHandled=!0),Pt({preventDefault:function(){},deltaX:t?e:0,deltaY:t?0:e}),!0))}),function(t,e,n){Xe(function(){var r=e.current;if(t&&r){var i,o,a=!1,s=function(){fu.cancel(i)},c=function t(){s(),i=fu(function(){n(o),t()})},l=function(t){if(!t.target.draggable&&0===t.button){var e=t;e._virtualHandled||(e._virtualHandled=!0,a=!0)}},u=function(){a=!1,s()},d=function(t){if(a){var e=_u(t,!1),n=r.getBoundingClientRect(),i=n.top,l=n.bottom;e<=i?(o=-bu(i-e),c()):e>=l?(o=bu(e-l),c()):s()}};return r.addEventListener("mousedown",l),r.ownerDocument.addEventListener("mouseup",u),r.ownerDocument.addEventListener("mousemove",d),function(){r.removeEventListener("mousedown",l),r.ownerDocument.removeEventListener("mouseup",u),r.ownerDocument.removeEventListener("mousemove",d),s()}}},[t])}(I,z,function(t){nt(function(e){return e+t})}),ru(function(){function t(t){var e=_t&&t.detail<0,n=wt&&t.detail>0;!j||e||n||t.preventDefault()}var e=z.current;return e.addEventListener("wheel",Pt,{passive:!1}),e.addEventListener("DOMMouseScroll",Mt,{passive:!0}),e.addEventListener("MozMousePixelScroll",t,{passive:!1}),function(){e.removeEventListener("wheel",Pt),e.removeEventListener("DOMMouseScroll",Mt),e.removeEventListener("MozMousePixelScroll",t)}},[j,_t,wt]),ru(function(){if(m){var t=Rt(K);G(t),Ct({x:t})}},[ft.width,m]);var Ft=function(){var t,e;null===(t=pt.current)||void 0===t||t.delayHidden(),null===(e=mt.current)||void 0===e||e.delayHidden()},jt=function(t,e,n,r,i,o,a,s){var c=Ze(),l=Xc(Ge(null),2),u=l[0],d=l[1];return ru(function(){if(u&&u.times<10){if(!t.current)return void d(function(t){return Gc({},t)});o();var s=u.targetAlign,c=u.originAlign,l=u.index,f=u.offset,h=t.current.clientHeight,p=!1,m=s,v=null;if(h){for(var y=s||c,g=0,b=0,_=0,w=Math.min(e.length-1,l),x=0;x<=w;x+=1){var k=i(e[x]);b=g;var S=n.get(k);g=_=b+(void 0===S?r:S)}for(var O="top"===y?f:h-f,E=w;E>=0;E-=1){var C=i(e[E]),T=n.get(C);if(void 0===T){p=!0;break}if((O-=T)<=0)break}switch(y){case"top":v=b-f;break;case"bottom":v=_-h+f;break;default:var R=t.current.scrollTop;b<R?m="top":_>R+h&&(m="bottom")}null!==v&&a(v),v!==u.lastTop&&(p=!0)}p&&d(Gc(Gc({},u),{},{times:u.times+1,targetAlign:m,lastTop:v}))}},[u,t.current]),function(t){if(null!=t){if(fu.cancel(c.current),"number"==typeof t)a(t);else if(t&&"object"===$c(t)){var n,r=t.align;n="index"in t?t.index:e.findIndex(function(e){return i(e)===t.key});var o=t.offset;d({times:0,index:n,offset:void 0===o?0:o,originAlign:r})}}else s()}}(z,U,M,a,T,function(){return P(!0)},nt,Ft);tn(e,function(){return{nativeElement:W.current,getScrollInfo:Ot,scrollTo:function(t){var e;(e=t)&&"object"===$c(e)&&("left"in e||"top"in e)?(void 0!==t.left&&G(Rt(t.left)),jt(t.top)):jt(t)}}}),ru(function(){if(w){var t=U.slice(ct,lt+1);w(t,U)}},[ct,lt,U]);var Dt=function(t,e,n,r){var i=Xc(en(function(){return[new Map,[]]},[t,n.id,r]),2),o=i[0],a=i[1];return function(i){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,c=o.get(i),l=o.get(s);if(void 0===c||void 0===l)for(var u=t.length,d=a.length;d<u;d+=1){var f,h=t[d],p=e(h);o.set(p,d);var m=null!==(f=n.get(p))&&void 0!==f?f:r;if(a[d]=(a[d-1]||0)+m,p===i&&(c=d),p===s&&(l=d),void 0!==c&&void 0!==l)break}return{top:a[c-1]||0,bottom:a[l]}}}(U,T,M,a),It=null==k?void 0:k({start:ct,end:lt,virtual:I,offsetX:K,offsetY:ut,rtl:q,getSize:Dt}),qt=function(t,e,n,r,i,o,a,s){var c=s.getKey;return t.slice(e,n+1).map(function(t,n){var s=a(t,e+n,{style:{width:r},offsetX:i});return b(ou,{key:c(t),setRef:function(e){return o(t,e)}},s)})}(U,ct,lt,m,K,N,d,et),Lt=null;o&&(Lt=Gc(Vc({},c?"height":"maxHeight",o),Ou),j&&(Lt.overflowY="hidden",m&&(Lt.overflowX="hidden"),X&&(Lt.pointerEvents="none")));var Ut={};return q&&(Ut.dir="rtl"),b("div",Hc({ref:W,style:Gc(Gc({},l),{},{position:"relative"}),className:L},Ut,C),b(tu,{onResize:function(t){ht({width:t.offsetWidth,height:t.offsetHeight})}},b(y,{className:"".concat(r,"-holder"),style:Lt,ref:z,onScroll:function(t){var e=t.currentTarget.scrollTop;e!==$&&nt(e),null==g||g(t),Ct()},onMouseEnter:Ft},b(iu,{prefixCls:r,height:st,offsetX:K,offsetY:ut,scrollWidth:m,onInnerResize:P,ref:B,innerProps:x,rtl:q,extra:It},qt))),I&&st>o&&b(wu,{ref:pt,prefixCls:r,scrollOffset:$,scrollRange:st,rtl:q,onScroll:Tt,onStartMove:Z,onStopMove:tt,spinSize:yt,containerSize:ft.height,style:null==S?void 0:S.verticalScrollBar,thumbStyle:null==S?void 0:S.verticalScrollBarThumb,showScrollBar:E}),I&&m>ft.width&&b(wu,{ref:mt,prefixCls:r,scrollOffset:K,scrollRange:m,rtl:q,onScroll:Tt,onStartMove:Z,onStopMove:tt,spinSize:vt,containerSize:ft.width,horizontal:!0,style:null==S?void 0:S.horizontalScrollBar,thumbStyle:null==S?void 0:S.horizontalScrollBarThumb,showScrollBar:E}))}var Cu=On(Eu);function Tu(t,e,{signal:n,edges:r}={}){let i,o=null;const a=null!=r&&r.includes("leading"),s=null==r||r.includes("trailing"),c=()=>{null!==o&&(t.apply(i,o),i=void 0,o=null)};let l=null;const u=()=>{null!=l&&clearTimeout(l),l=setTimeout(()=>{l=null,s&&c(),f()},e)},d=()=>{null!==l&&(clearTimeout(l),l=null)},f=()=>{d(),i=void 0,o=null},h=function(...t){if(n?.aborted)return;i=this,o=t;const e=null==l;u(),a&&e&&c()};return h.schedule=u,h.cancel=f,h.flush=()=>{d(),c()},n?.addEventListener("abort",f,{once:!0}),h}Cu.displayName="List";const Ru=new class{baseUrl="http://localhost:5111";async searchInstruments(t,e,n){try{return(await wc.get(`${this.baseUrl}/api/Instrument/find-all`,{params:{pageIndex:t,pageSize:e,keyword:n},headers:{Authorization:`Bearer ${qc()}`,"Content-Type":"application/json"}})).data}catch(r){if(wc.isAxiosError(r)){const t=r.response?.data?.message||`Failed to search instruments: ${r.message}`;throw new Error(t)}throw r}}},Au=({activeWatchlistId:t,activeWatchlistInstrumentIds:e=[]})=>{const[n,r]=Ge(""),[i,o]=Ge([]),[a,s]=Ge(!1),{addInstrumentMutation:c}=Uc(),l=nn(Tu(async t=>{if(t.trim())try{const e=await Ru.searchInstruments(1,25,t);o(e?.data?.items||[])}catch(e){console.error("Error searching instruments:",e),o([])}else o([])},500),[]),u=async e=>{if(t)try{c.mutate({instrumentId:parseInt(e.instrumentId||"0"),watchlistId:t}),ma.success(`${e.abbreviation} added to watchlist`)}catch{ma.error(`Failed to add ${e.abbreviation}`)}finally{r(""),s(!1)}};Xe(()=>{l(n)},[n,l]);return $("div",{className:"add-instrument-section",children:[$("h4",{className:"add-section-title",children:"Add Symbols"}),$("div",{className:"search-container",children:[$(Ko,{size:16,className:"search-icon"}),$("input",{type:"text",value:n,onChange:t=>{const e=t.currentTarget.value;r(e)},onFocus:()=>s(!0),onBlur:()=>s(!1),placeholder:"Search instruments to add...",className:"search-input"})]}),a&&$("div",{className:"search-results",onMouseDown:t=>t.preventDefault(),children:i.length>0?$(Cu,{data:i,height:200,itemHeight:60,itemKey:"instrumentId",children:t=>{return $("div",{className:"search-result-item",onClick:()=>u(t),children:[$("div",{className:"instrument-info",children:[$("span",{className:"symbol",children:t.ticker}),$("span",{className:"name",children:t.name}),$("span",{className:"abbreviation",children:t.abbreviation??"--"})]}),$("button",{className:"add-instrument-btn",onClick:()=>u(t),disabled:(n=t.instrumentId||"0",e.some(t=>t.toString()===n)),children:$(Vo,{size:16})})]},t.instrumentId);var n}}):$("div",{className:"search-results-empty",children:[$("p",{children:"No instruments found"}),$("p",{children:"Try a different search term"})]})})]})},Nu=({children:t})=>$("div",{className:"instruments-section",children:$("div",{className:"instruments-table-container",children:$("table",{className:"instruments-table",children:[$("thead",{children:$("tr",{children:[$("th",{className:"table-header",children:"Symbol"}),$("th",{className:"table-header",children:"Name"}),$("th",{className:"table-header",children:"Price"}),$("th",{className:"table-header",children:"Change"}),$("th",{className:"table-header",children:"High"}),$("th",{className:"table-header",children:"Low"}),$("th",{className:"table-header",children:"Week High"}),$("th",{className:"table-header",children:"Volume"}),$("th",{className:"table-header",children:"Actions"})]})}),$("tbody",{children:$("tr",{children:$("td",{colSpan:9,children:$("div",{className:"loading-state",children:$("p",{children:t})})})})})]})})}),Pu=({instruments:t,isLoading:e,activeWatchlistId:n})=>{const{removeInstrumentMutation:r}=Uc(),[i,o]=Ge();Xe(()=>{if(t&&t.length>0){const e={id:t[0].id,symbol:t[0].symbol};o(e)}},[t]),Xe(()=>{const t=window.EurolandAppContext;if(t)return t.registerCommandHandler("instrument-selected",()=>i),t.emit("instrument-selected",i),()=>{t.unregisterCommandHandler("instrument-selected",()=>i)}},[i]);return e?$(Nu,{children:$("div",{className:"loading-state",children:$("p",{children:"Loading instruments..."})})}):0===t.length?$(Nu,{children:$("div",{className:"empty-state",children:[$("p",{children:"No instruments in this watchlist"}),$("p",{children:"Use the search below to add instruments"})]})}):$("div",{className:"instruments-section",children:$("div",{className:"instruments-table-container",children:$("table",{className:"instruments-table",children:[$("thead",{children:$("tr",{children:[$("th",{className:"table-header",children:"Symbol"}),$("th",{className:"table-header",children:"Last"}),$("th",{className:"table-header",children:"Change"}),$("th",{className:"table-header",style:{textAlign:"end"},children:"Change%"})]})}),$("tbody",{children:t.map(t=>$("tr",{className:"table-row "+(i?.id===t.id?"selected":""),onClick:()=>(t=>{const e={id:t.id,symbol:t.symbol};o(e)})(t),children:[$("td",{className:"table-row cell symbol",children:[$("span",{className:"symbol-text",children:t.symbol}),$("span",{className:"market-text",children:t.market})]}),$("td",{className:"table-row cell price",children:t?.last?.toFixed(2)?t?.last?.toFixed(2)+t.currency:"--"}),$("td",{className:"table-row cell change "+(t.change>=0?"positive":"negative"),children:[t.change>=0?"+":"",t.change?.toFixed(2)??"--"]}),$("td",{className:"table-row cell change-percent "+(t.changePercent>=0?"positive":"negative"),children:[t.changePercent>=0?"+":"",t.changePercent?.toFixed(2)?t.changePercent?.toFixed(2)+"%":"--",$("button",{className:"remove-btn",onClick:()=>(async t=>{if(n)try{r.mutate({watchlistId:n,instrumentId:parseInt(t)}),ma.success("Instrument removed from watchlist")}catch{ma.error("Failed to remove instrument")}})(t.id),children:$(Jo,{size:16})})]})]},t.id))})]})})})},Mu=(function(t){for(var e=new Map,n=[],r=[],i=Array.isArray(t)?t[0]:t||"",o=1;o<arguments.length;o++){var a=arguments[o];a&&a.definitions?r.push(a):i+=a,i+=arguments[0][o]}r.unshift(ue(i));for(var s=0;s<r.length;s++)for(var c=0;c<r[s].definitions.length;c++){var l=r[s].definitions[c];if(l.kind===Y){var u=l.name.value,d=ce(l);e.has(u)||(e.set(u,d),n.push(l))}else n.push(l)}return ue({kind:G,definitions:n})})`
query Ticker($ids: [Int!]!, $adjClose: Boolean, $toCurrency: String) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    shareName
    id
    symbol
    market {
      translation {
        cultureName
        value
      }
      status {
        isOpened
        remainingTime
      }
    }
    currency {
      code
      name
    }
    currentPrice {
      open
      date
      bid
      ask
      high
      low
      volume
      officialClose
      officialCloseDate
      # Chỉ query tickerData nếu cần thiết
      tickerData {
        last
        change
        changePercentage
        prevClose
      }
    }
    fifty_two_weeks: performance(period: FIFTY_TWO_WEEKS) {
      highest
      lowest
      changePercentage
    }
  }
}
`,Fu=()=>{const{watchlistsQuery:t,createWatchlistMutation:e}=Uc(),{data:n,isLoading:r,error:i}=t,{activeWatchlistId:o,setActiveWatchlistId:a,activeWatchlist:s,watchlistsArray:c}=(({watchlists:t})=>{const[e,n]=Ge(null),r=en(()=>t||[],[t]);Xe(()=>{r.length>0&&null===e&&n(r[0].id),e&&r.length>0&&(r.some(t=>t.id===e)||n(r[0].id))},[r,e]);const i=en(()=>e&&0!==r.length&&r.find(t=>t.id===e)||null,[r,e]);return{activeWatchlistId:e,setActiveWatchlistId:n,activeWatchlist:i,watchlistsArray:r}})({watchlists:n?.data||[]}),{instruments:l,isLoading:u}=(({activeWatchlist:t})=>{const[e]=lr({query:Mu,variables:{adjClose:!1,ids:t?.instrumentIds||[]}}),{data:n,fetching:r}=e;return{instruments:en(()=>n?.instrumentByIds?n.instrumentByIds?.filter(t=>null!==t).map(t=>({id:t?.id?.toString(),instrumentId:t?.id?.toString(),symbol:t?.symbol,name:t?.shareName,price:t?.currentPrice?.tickerData?.last,change:t?.currentPrice?.tickerData?.change,last:t?.currentPrice?.tickerData?.last,high:t?.currentPrice?.high,low:t?.currentPrice?.low,hightOfWeek:t?.fifty_two_weeks?.highest,changePercent:t?.currentPrice?.tickerData?.changePercentage,volume:t?.currentPrice?.volume,currency:t?.currency?.code,market:t?.market?.translation?.value,marketStatus:t?.market?.status,fiftyTwoWeeks:t?.fifty_two_weeks})):[],[n]),isLoading:r}})({activeWatchlist:s}),d=async t=>{if(t.trim())try{e.mutate(t),ma.success("Watchlist created successfully")}catch{ma.error("Failed to create watchlist")}else ma.error("Watchlist name cannot be empty")};return r?$("div",{className:"loading-spinner",children:$("div",{className:"spinner"})}):i?$("div",{className:"error-message",children:[$("div",{children:"Error loading watchlists"}),$("div",{children:"Let's try again"})]}):r||0!==c.length?$("div",{className:"watchlist-container",children:[$(Wc,{watchlists:c,activeWatchlistId:o,onWatchlistSelect:a,onCreateWatchlist:d}),$(Au,{activeWatchlistId:o,activeWatchlistInstrumentIds:s?.instrumentIds}),$("div",{className:"content-area",children:s&&$(Pu,{instruments:l,isLoading:u,activeWatchlistId:o})})]}):$(Bc,{onCreateWatchlist:d})};window.addEventListener("error",t=>{if(t.message.includes("ResizeObserver loop"))return t.stopImmediatePropagation(),!1});const ju=new class{constructor(t={}){this.queryCache=t.queryCache||new Qr,this.mutationCache=t.mutationCache||new Kr,this.logger=t.logger||Br,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,1===this.mountCount&&(this.unsubscribeFocus=Fr.subscribe(()=>{Fr.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=Dr.subscribe(()=>{Dr.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var t,e;this.mountCount--,0===this.mountCount&&(null==(t=this.unsubscribeFocus)||t.call(this),this.unsubscribeFocus=void 0,null==(e=this.unsubscribeOnline)||e.call(this),this.unsubscribeOnline=void 0)}isFetching(t,e){const[n]=gr(t,e);return n.fetchStatus="fetching",this.queryCache.findAll(n).length}isMutating(t){return this.mutationCache.findAll({...t,fetching:!0}).length}getQueryData(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state.data}ensureQueryData(t,e,n){const r=yr(t,e,n),i=this.getQueryData(r.queryKey);return i?Promise.resolve(i):this.fetchQuery(r)}getQueriesData(t){return this.getQueryCache().findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,n){const r=this.queryCache.find(t),i=function(t,e){return"function"==typeof t?t(e):t}(e,null==r?void 0:r.state.data);if(void 0===i)return;const o=yr(t),a=this.defaultQueryOptions(o);return this.queryCache.build(this,a).setData(i,{...n,manual:!0})}setQueriesData(t,e,n){return Wr.batch(()=>this.getQueryCache().findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,n)]))}getQueryState(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state}removeQueries(t,e){const[n]=gr(t,e),r=this.queryCache;Wr.batch(()=>{r.findAll(n).forEach(t=>{r.remove(t)})})}resetQueries(t,e,n){const[r,i]=gr(t,e,n),o=this.queryCache,a={type:"active",...r};return Wr.batch(()=>(o.findAll(r).forEach(t=>{t.reset()}),this.refetchQueries(a,i)))}cancelQueries(t,e,n){const[r,i={}]=gr(t,e,n);void 0===i.revert&&(i.revert=!0);const o=Wr.batch(()=>this.queryCache.findAll(r).map(t=>t.cancel(i)));return Promise.all(o).then(pr).catch(pr)}invalidateQueries(t,e,n){const[r,i]=gr(t,e,n);return Wr.batch(()=>{var t,e;if(this.queryCache.findAll(r).forEach(t=>{t.invalidate()}),"none"===r.refetchType)return Promise.resolve();const n={...r,type:null!=(t=null!=(e=r.refetchType)?e:r.type)?t:"active"};return this.refetchQueries(n,i)})}refetchQueries(t,e,n){const[r,i]=gr(t,e,n),o=Wr.batch(()=>this.queryCache.findAll(r).filter(t=>!t.isDisabled()).map(t=>{var e;return t.fetch(void 0,{...i,cancelRefetch:null==(e=null==i?void 0:i.cancelRefetch)||e,meta:{refetchPage:r.refetchPage}})}));let a=Promise.all(o).then(pr);return null!=i&&i.throwOnError||(a=a.catch(pr)),a}fetchQuery(t,e,n){const r=yr(t,e,n),i=this.defaultQueryOptions(r);void 0===i.retry&&(i.retry=!1);const o=this.queryCache.build(this,i);return o.isStaleByTime(i.staleTime)?o.fetch(i):Promise.resolve(o.state.data)}prefetchQuery(t,e,n){return this.fetchQuery(t,e,n).then(pr).catch(pr)}fetchInfiniteQuery(t,e,n){const r=yr(t,e,n);return r.behavior=Gr(),this.fetchQuery(r)}prefetchInfiniteQuery(t,e,n){return this.fetchInfiniteQuery(t,e,n).then(pr).catch(pr)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(t){this.defaultOptions=t}setQueryDefaults(t,e){const n=this.queryDefaults.find(e=>xr(t)===xr(e.queryKey));n?n.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})}getQueryDefaults(t){if(!t)return;const e=this.queryDefaults.find(e=>kr(t,e.queryKey));return null==e?void 0:e.defaultOptions}setMutationDefaults(t,e){const n=this.mutationDefaults.find(e=>xr(t)===xr(e.mutationKey));n?n.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})}getMutationDefaults(t){if(!t)return;const e=this.mutationDefaults.find(e=>kr(t,e.mutationKey));return null==e?void 0:e.defaultOptions}defaultQueryOptions(t){if(null!=t&&t._defaulted)return t;const e={...this.defaultOptions.queries,...this.getQueryDefaults(null==t?void 0:t.queryKey),...t,_defaulted:!0};return!e.queryHash&&e.queryKey&&(e.queryHash=wr(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.useErrorBoundary&&(e.useErrorBoundary=!!e.suspense),e}defaultMutationOptions(t){return null!=t&&t._defaulted?t:{...this.defaultOptions.mutations,...this.getMutationDefaults(null==t?void 0:t.mutationKey),...t,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5}}});class Du extends HTMLElement{container=null;constructor(){super(),this.attachShadow({mode:"open"});const t=document.createElement("style");t.textContent=dr,t.textContent+=".content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .watchlist-container {\r\n    margin: 0;\r\n    border-radius: 0;\r\n  }\r\n}\r\n/* Loading and Error States */\r\n.loading-spinner {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  color: #6c757d;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.spinner {\r\n  border: 4px solid #f3f3f3;\r\n  border-top: 4px solid #3498db;\r\n  border-radius: 50%;\r\n  width: 50px;\r\n  height: 50px;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.error-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  font-size: 1.2rem;\r\n  padding: 1rem;\r\n  text-align: center;\r\n}",t.textContent+="/* Tab Bar Styles */\r\n.tab-bar {\r\n  border-bottom: 1px solid #e1e3e6;\r\n  padding: 0;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n  min-width: min-content;\r\n}\r\n\r\n.tab {\r\n  display: flex;\r\n  align-items: center;\r\n  min-width: 120px;\r\n  max-width: 240px;\r\n  height: 30px;\r\n  background: #e9ecef;\r\n  border: 1px solid #d1d4dc;\r\n  border-bottom: none;\r\n  border-radius: 4px 4px 0 0;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  position: relative;\r\n  padding: 0 8px;\r\n  margin-bottom: 1px;\r\n}\r\n\r\n.tab:hover {\r\n  background: #dee2e6;\r\n}\r\n\r\n.tab.active {\r\n  background: #ffffff;\r\n  border-color: #e1e3e6;\r\n  margin-bottom: 0;\r\n  z-index: 1;\r\n}\r\n\r\n.tab-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.tab-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #131722;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  flex: 1;\r\n}\r\n\r\n.tab-star {\r\n  color: #ffc107;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tab-count {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tab-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  opacity: 0;\r\n  transition: opacity 0.2s;\r\n  margin-left: 16px;\r\n}\r\n\r\n.tab:hover .tab-actions {\r\n  opacity: 1;\r\n}\r\n\r\n.tab.active .tab-actions {\r\n  opacity: 1;\r\n}\r\n\r\n.tab-action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  border-radius: 3px;\r\n  background: transparent;\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\nbutton.tab-action-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n.tab-action-btn:hover {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  color: #131722;\r\n}\r\n\r\n.close-tab-btn:hover {\r\n  background: rgba(244, 67, 54, 0.1);\r\n  color: #f44336;\r\n}\r\n\r\n.tab-action-btn:disabled {\r\n  opacity: 0.3;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.tab-action-btn:disabled:hover {\r\n  background: transparent;\r\n  color: #6c757d;\r\n}\r\n\r\n/* Tab Edit Form */\r\n.tab-edit-form {\r\n  flex: 1;\r\n  padding: 2px;\r\n}\r\n\r\n.tab-edit-input {\r\n  width: 100%;\r\n  padding: 4px 6px;\r\n  border: 1px solid #2962ff;\r\n  border-radius: 3px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-edit-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n/* Delete Confirmation */\r\n.delete-confirm {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n.confirm-delete-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 3px;\r\n  border:1px solid #f44336;\r\n  color: #f44336;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.confirm-delete-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n.cancel-delete-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 3px;\r\n  border:1px solid #6c757d;\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.cancel-delete-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n/* Add Tab */\r\n.add-tab-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #e9ecef;\r\n  border: 1px solid #d1d4dc;\r\n  border-bottom: none;\r\n  border-radius: 4px 4px 0 0;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  color: #6c757d;\r\n  margin-bottom: 1px;\r\n  flex-shrink: 0;\r\n}\r\n\r\nbutton.add-tab-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  padding: 0;\r\n}\r\n\r\n.add-tab-btn:hover {\r\n  background: #dee2e6;\r\n  color: #131722;\r\n}\r\n\r\n.add-tab-form {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  min-width: 200px;\r\n  height: 30px;\r\n  background: #ffffff;\r\n  border: 1px solid #2962ff;\r\n  border-bottom: none;\r\n  border-radius: 4px 4px 0 0;\r\n  padding: 0 8px;\r\n  margin-bottom: 1px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.add-tab-input {\r\n  flex: 1;\r\n  padding: 4px 6px;\r\n  border: none;\r\n  background: transparent;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  min-width: 100px; /* Ensure minimum width */\r\n}\r\n\r\n.add-tab-input:focus {\r\n  outline: none;\r\n}\r\n\r\n.add-tab-input::placeholder {\r\n  color: #6c757d;\r\n}\r\n\r\n.add-tab-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.confirm-add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #00c853;\r\n  border-radius: 3px;\r\n  color: #00c853;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  flex-shrink: 0;\r\n}\r\n\r\nbutton.confirm-add-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n.cancel-add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 3px;\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  flex-shrink: 0;\r\n}\r\n\r\nbutton.cancel-add-btn {\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .tab {\r\n    min-width: 120px;\r\n    max-width: 180px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .tab {\r\n    min-width: 120px;\r\n    max-width: 120px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  .tab-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .tab-count {\r\n    display: none;\r\n  }\r\n}\r\n",t.textContent+="/* Empty Watchlist State */\r\n.empty-watchlist-state {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.empty-watchlist-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  max-width: 400px;\r\n  width: 100%;\r\n}\r\n\r\n.empty-watchlist-icon {\r\n  margin-bottom: 12px;\r\n  color: #6c757d;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-watchlist-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.empty-watchlist-description {\r\n  margin: 0 0 24px 0;\r\n  font-size: 16px;\r\n  color: #6c757d;\r\n  line-height: 1.5;\r\n}\r\n\r\n.create-first-watchlist-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 16px;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.create-first-watchlist-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(41, 98, 255, 0.3);\r\n}\r\n\r\n.first-watchlist-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  width: 100%;\r\n  max-width: 300px;\r\n  padding: 24px;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.first-watchlist-input {\r\n  padding: 8px 12px;\r\n  border: 1px solid #d1d4dc;\r\n  border-radius: 8px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 16px;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.first-watchlist-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n  box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);\r\n}\r\n\r\n.first-watchlist-input::placeholder {\r\n  color: #6c757d;\r\n}\r\n\r\n.first-watchlist-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.confirm-first-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex: 1;\r\n  padding: 6px 10px;\r\n  border: 1px solid #00c853;\r\n  color: #00c853;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  justify-content: center;\r\n}\r\n\r\n.cancel-first-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex: 1;\r\n  padding: 6px 10px;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  justify-content: center;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 480px) {\r\n  /* Empty Watchlist State - Mobile */\r\n  .empty-watchlist-state {\r\n    padding: 20px 16px;\r\n  }\r\n\r\n  .empty-watchlist-title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .empty-watchlist-description {\r\n    font-size: 14px;\r\n    margin-bottom: 24px;\r\n  }\r\n\r\n  .create-first-watchlist-btn {\r\n    padding: 10px 20px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .first-watchlist-form {\r\n    padding: 20px;\r\n    max-width: 280px;\r\n  }\r\n\r\n  .first-watchlist-actions {\r\n    gap: 8px;\r\n  }\r\n}\r\n",t.textContent+="/* Add Instrument Section */\r\n.add-instrument-section {\r\n  position: relative;\r\n  width: 100%;\r\n  padding: 8px 0;\r\n  border-top: 1px solid #e1e3e6;\r\n}\r\n\r\n.add-section-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #6c757d;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 10px 8px 10px 24px;\r\n  border: 1px solid #d1d4dc;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.search-results {\r\n  position: absolute;\r\n  top: 84px;\r\n  left: 0;\r\n  right: 0;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 4%;\r\n  background: #ffffff;\r\n  margin-bottom: 16px;\r\n  z-index: 999;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Empty State */\r\n.search-results-empty {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 24px;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  min-height: 120px;\r\n}\r\n\r\n.search-results-empty-icon {\r\n  font-size: 24px;\r\n  margin-bottom: 8px;\r\n  color: #d1d4dc;\r\n}\r\n\r\n.search-results-empty-text {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* Virtual List Styles */\r\n.search-results .rc-virtual-list {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-results .rc-virtual-list-holder {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-results .rc-virtual-list-holder-inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-result-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  cursor: pointer;\r\n  padding: 12px 16px;\r\n  gap: 4px;\r\n  border-bottom: 1px solid #f1f3f4;\r\n  transition: background-color 0.2s;\r\n  height: 60px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.search-result-item:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.search-result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.instrument-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  flex: 1;\r\n}\r\n\r\n.instrument-info .symbol {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  min-width: 60px;\r\n}\r\n\r\n.instrument-info .name {\r\n  color: #6c757d;\r\n  flex: 1;\r\n}\r\n\r\n.instrument-info .abbreviation {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  min-width: 80px;\r\n}\r\n\r\n.instrument-info .change {\r\n  font-weight: 500;\r\n  min-width: 100px;\r\n}\r\n\r\n.instrument-info .change.positive {\r\n  color: #00c853;\r\n}\r\n\r\n.instrument-info .change.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.add-instrument-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #a4a6ac;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding-left: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.add-instrument-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  padding: 0;\r\n}\r\n\r\nbutton.add-instrument-btn:hover {\r\n  background: #d8dbe2;\r\n  color: white;\r\n}\r\n\r\nbutton.add-instrument-btn:disabled {\r\n  background: #d1d4dc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .instrument-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .instrument-info .symbol,\r\n  .instrument-info .price,\r\n  .instrument-info .change {\r\n    min-width: auto;\r\n  }\r\n}\r\n",t.textContent+="/* Instruments Section */\n.instruments-section {\n  flex: 1;\n  padding: 10px 0 0 0;\n  overflow-y: auto;\n  max-height: calc(100vh - 430px);\n  display: flex;\n  flex-direction: column;\n}\n\n.instruments-table-container {\n  width: 100%;\n  overflow-y: auto;\n  max-height: calc(100vh - 430px);\n  border: 1px solid #e1e3e6;\n  border-radius: 6px;\n}\n\n.instruments-table {\n  width: 100%;\n  border-collapse: collapse;\n  background: #ffffff;\n  table-layout: fixed;\n}\n\n.instruments-table thead {\n  position: sticky;\n  top: 0;\n  z-index: 1;\n}\n\n.table-header {\n  background: #f8f9fa;\n  padding: 8px 12px;\n  font-weight: 600;\n  font-size: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  color: #6c757d;\n  border-bottom: 1px solid #e1e3e6;\n  text-align: left;\n}\n\n.table-row {\n  display: table-row;\n  position: relative;\n  cursor: pointer;\n  border-bottom: 1px solid #f1f3f4;\n  transition: background-color 0.2s;\n}\n\n.table-row td {\n  display: table-cell;\n  padding: 8px 12px;\n  vertical-align: middle;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n}\n\n.symbol {\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.symbol-text {\n  font-weight: 600;\n}\n\n.market-text {\n  font-size: 11px;\n  color: #6c757d;\n}\n\n.positive {\n  color: #28a745;\n}\n\n.negative {\n  color: #dc3545;\n}\n\n.remove-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: #dc3545;\n  padding: 0;\n}\n\n.table-row:hover .remove-btn {\n  visibility: visible;\n}\n\n.table-row.selected {\n  background: #e3f2fd;\n}\n\n.table-row:last-child {\n  border-bottom: none;\n}\n\n.cell {\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.cell.symbol {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 2px;\n}\n\n\n.symbol-text {\n  font-weight: 600;\n  color: #131722;\n}\n\n.market-text {\n  font-size: 11px;\n  color: #6c757d;\n  font-weight: 400;\n}\n\n.cell.name {\n  color: #6c757d;\n  font-weight: 400;\n}\n\n.cell.price {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.change {\n  font-weight: 500;\n}\n\n.cell.change-percent {\n  text-align: end;\n  font-weight: 500;\n}\n\n.cell.change.positive {\n  color: #00c853;\n}\n\n.cell.change.negative {\n  color: #f44336;\n}\n\n.cell.change-percent.positive {\n  color: #00c853;\n}\n\n.cell.change-percent.negative {\n  color: #f44336;\n}\n\n.cell.high {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.low {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.week-high {\n  color: #131722;\n  font-weight: 600;\n}\n\n.remove-btn {\n  background: none;\n  visibility: hidden;\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  background: white;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 4px;\n  color: #6c757d;\n  transition: all 0.2s;\n}\n\n.remove-btn:hover {\n  background: rgba(243, 147, 140);\n  color: #f44336;\n}\n\n.delete-confirm {\n  display: flex;\n  gap: 4px;\n}\n\n.confirm-delete-btn,\n.cancel-delete-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s;\n}\n\n.confirm-delete-btn {\n  color: #00c853;\n}\n\n.confirm-delete-btn:hover {\n  background: rgba(0, 200, 83, 0.1);\n}\n\n.cancel-delete-btn {\n  color: #6c757d;\n}\n\n.cancel-delete-btn:hover {\n  background: rgba(108, 117, 125, 0.1);\n}\n\n.empty-state,\n.loading-state,\n.error-state {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  text-align: center;\n  color: #6c757d;\n}\n\n.empty-state p,\n.loading-state p,\n.error-state p {\n  margin: 4px 0;\n  font-size: 14px;\n}\n\n.error-state {\n  color: #f44336;\n}",this.container=document.createElement("div"),this.container.id="preact-root",this.shadowRoot.appendChild(t),this.shadowRoot.appendChild(this.container)}connectedCallback(){this.container&&U($(ko,{client:ju,children:$(ir,{value:ur,children:$(Fu,{})})}),this.container)}disconnectedCallback(){this.container&&U(null,this.container)}}class Iu extends HTMLElement{container=null;constructor(){super(),this.attachShadow({mode:"open"});const t=document.createElement("style");t.textContent=dr,t.textContent+="/* Add Instrument Widget */\r\n.add-instrument-container {\r\n  display: flex;\r\n}\r\n\r\n.add-instrument-widget {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.add-instrument-trigger {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  border: 1px solid #e1e3e6;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  padding: 6px 10px;\r\n}\r\n\r\n.add-instrument-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 8px;\r\n}\r\n",t.textContent+=".modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    padding: 20px;\r\n  }\r\n  \r\n  .watchlist-modal {\r\n    background: white;\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 480px;\r\n    min-height: 60vh;\r\n    max-height: 80vh;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .modal-header {\r\n    padding: 10px;\r\n    border-bottom: 1px solid #e1e3e6;\r\n    background: #f8f9fa;\r\n  }\r\n  \r\n  .modal-title {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .modal-title h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #131722;\r\n  }\r\n  \r\n  .modal-close {\r\n    background: none;\r\n    border: none;\r\n    cursor: pointer;\r\n    padding: 4px;\r\n    color: #6c757d;\r\n    transition: all 0.2s;\r\n  }\r\n  \r\n  .modal-close:hover {\r\n    background: #e9ecef;\r\n    color: #131722;\r\n  }\r\n  \r\n  .instrument-preview {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 12px 16px;\r\n    background: white;\r\n    border: 1px solid #e1e3e6;\r\n  }\r\n  \r\n  .instrument-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 2px;\r\n  }\r\n  \r\n  .instrument-symbol {\r\n    font-weight: 600;\r\n    color: #131722;\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .instrument-name {\r\n    color: #6c757d;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .instrument-price {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: flex-end;\r\n    gap: 2px;\r\n  }\r\n  \r\n  .price {\r\n    font-weight: 600;\r\n    color: #131722;\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .change {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .change.positive {\r\n    color: #00c853;\r\n  }\r\n  \r\n  .change.negative {\r\n    color: #f44336;\r\n  }\r\n  \r\n  .modal-content {\r\n    flex: 1;\r\n    overflow: hidden;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 10px 24px 0;\r\n  }\r\n  \r\n  .search-container {\r\n    position: relative;\r\n  }\r\n  \r\n  .search-icon {\r\n    position: absolute;\r\n    left: 12px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    color: #6c757d;\r\n  }\r\n  \r\n  .search-input {\r\n    width: 100%;\r\n    padding: 6px 6px 6px 32px;\r\n    border: 1px solid #d1d4dc;\r\n    background: #ffffff;\r\n    color: #131722;\r\n    font-size: 14px;\r\n    border-radius: 4px;\r\n  }\r\n  \r\n  .search-input:focus {\r\n    outline: none;\r\n    border-color: #2962ff;\r\n  }\r\n  \r\n  .watchlists-section {\r\n    flex: 1;\r\n    padding: 10px 24px;\r\n    overflow: hidden;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .section-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .section-header h4 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #131722;\r\n  }\r\n  \r\n  .create-new-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    padding: 4px 8px;\r\n    color: #333;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    transition: background-color 0.2s;\r\n    border: 1px solid #e1e3e6;\r\n    border-radius: 4px;\r\n  }\r\n  \r\n  .create-new-btn:hover:not(:disabled) {\r\n    color: black;\r\n  }\r\n  \r\n  .create-new-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n  }\r\n  \r\n  .watchlists-list {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .create-watchlist-form {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 16px;\r\n    border: 1px solid #e1e3e6;\r\n    margin-bottom: 8px;\r\n  }\r\n  \r\n  .create-input {\r\n    flex: 1;\r\n    padding: 8px 12px;\r\n    border: 1px solid #d1d4dc;\r\n    background: white;\r\n    color: #131722;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .create-input:focus {\r\n    outline: none;\r\n    border-color: #2962ff;\r\n  }\r\n  \r\n  .create-actions {\r\n    display: flex;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .confirm-btn,\r\n  .cancel-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: none;\r\n    cursor: pointer;\r\n    transition: background-color 0.2s;\r\n  }\r\n  \r\n  button.confirm-btn,\r\n  button.cancel-btn {\r\n    width: 28px;\r\n    height: 28px;\r\n    padding: 0;\r\n  }\r\n  \r\n  .confirm-btn {\r\n    border: 1px solid #00c853;\r\n    color: #00c853;\r\n  }\r\n  \r\n  .confirm-btn:hover:not(:disabled) {\r\n    border: 1px solid #05f168;\r\n    color: #05f168;}\r\n  \r\n  .cancel-btn {\r\n    border: 1px solid #6c757d;\r\n    color: #6c757d;\r\n  }\r\n  \r\n  .cancel-btn:hover:not(:disabled) {\r\n    border: 1px solid #333;\r\n    color: #333;}\r\n  \r\n  .watchlist-item {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 12px;\r\n    cursor: pointer;\r\n    background: white;\r\n    border: 1px solid #e1e3e6;\r\n    transition: all 0.2s;\r\n    border-radius: 4px;\r\n  }\r\n  \r\n  .watchlist-item:hover {\r\n    background: #f8f9fa;\r\n    box-shadow: 0 2px 4px rgba(41, 98, 255, 0.1);\r\n  }\r\n  \r\n  .watchlist-info {\r\n    flex: 1;\r\n  }\r\n  \r\n  .watchlist-name {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    font-weight: 500;\r\n    color: #131722;\r\n    font-size: 16px;\r\n    margin-bottom: 2px;\r\n  }\r\n  \r\n  .default-star {\r\n    color: #ffc107;\r\n  }\r\n  \r\n  .watchlist-count {\r\n    color: #6c757d;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .add-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #f8f9fa;\r\n    cursor: pointer;\r\n    color: #333;\r\n    transition: all 0.2s;\r\n  }\r\n  \r\n  button.add-btn {\r\n    width: 36px;\r\n    height: 36px;\r\n    padding: 0;\r\n  }\r\n  \r\n  .add-btn:hover:not(:disabled) {\r\n    color: black;\r\n  }\r\n  \r\n  .add-btn:disabled {\r\n    cursor: not-allowed;\r\n    opacity: 0.6;\r\n  }\r\n  \r\n  .empty-state {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n    color: #6c757d;\r\n  }\r\n  \r\n  .empty-state p {\r\n    margin: 0;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  /* Loading Spinner */\r\n  .loading-spinner {\r\n    width: 20px;\r\n    height: 20px;\r\n    border: 2px solid #f3f3f3;\r\n    border-top: 2px solid #2962ff;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n  }\r\n  \r\n  .loading-spinner.small {\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n\r\n  .border-radius {\r\n    border-radius: 6px;\r\n  }\r\n  \r\n  @keyframes spin {\r\n    0% {\r\n      transform: rotate(0deg);\r\n    }\r\n    100% {\r\n      transform: rotate(360deg);\r\n    }\r\n  }\r\n  \r\n  /* Responsive Design */\r\n  @media (max-width: 768px) {\r\n    .modal-overlay {\r\n      padding: 10px;\r\n    }\r\n  \r\n    .watchlist-modal {\r\n      max-height: 90vh;\r\n    }\r\n  \r\n    .modal-header,\r\n    .search-section,\r\n    .watchlists-section {\r\n      padding: 24px;\r\n    }\r\n  \r\n    .instrument-preview {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      gap: 8px;\r\n    }\r\n  \r\n    .instrument-price {\r\n      align-items: flex-start;\r\n    }\r\n  }\r\n  ",this.container=document.createElement("div"),this.container.id="preact-root",this.shadowRoot.appendChild(t),this.shadowRoot.appendChild(this.container)}connectedCallback(){this.container&&U($(ko,{client:ju,children:$(ir,{value:ur,children:$(zc,{instrumentId:this.getAttribute("instrumentId")})})}),this.container)}disconnectedCallback(){this.container&&U(null,this.container)}}customElements.define("euroland-watch-list",Du),customElements.define("euroland-add-instrument",Iu)});
//# sourceMappingURL=watchlist-widget.umd.js.map
