import { Plus } from "lucide-preact";
import type { Watchlist } from "../services/watchlistTypes";
import { WatchlistModal } from "./addInstrument/WatchlistModal";
import { useEffect, useState } from "preact/hooks";
import { useWatchlistQueries } from "../services/watchlistQueries";
import { toast } from "react-toastify";
import { useAuth } from "./watchlist/hooks/useAuth";

interface AddInstrumentWidgetProps {
  instrumentId: string;
  onSuccess?: (watchlistId: string, watchlistName: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

const AddInstrumentWidget = ({
  instrumentId,
  onSuccess,
  onError,
  className = "",
}: AddInstrumentWidgetProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const auth = useAuth();
  const { watchlistsQuery, addInstrumentMutation, createWatchlistMutation } =
    useWatchlistQueries();

  useEffect(() => {
    if (isModalOpen) {
      watchlistsQuery.refetch();
    }
  }, [isModalOpen, watchlistsQuery]);

  const handleAddToWatchlist = async (watchlistId: string) => {
    setIsLoading(true);
    try {
      await addInstrumentMutation.mutate({
        watchlistId,
        instrumentId: parseInt(instrumentId || "0"),
      });

      toast.success("Instrument added to watchlist");
      const watchlist = watchlistsQuery.data?.data?.find(
        (w: Watchlist) => w.id === watchlistId
      );
      onSuccess?.(watchlistId, watchlist?.name || "");
      watchlistsQuery.refetch();
    } catch (error) {
      onError?.("Failed to add instrument to watchlist");
      toast.error("Failed to add instrument");
      console.error("Error adding instrument to watchlist:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateWatchlist = async (name: string) => {
    setIsLoading(true);
    try {
      await createWatchlistMutation.mutate(name);
      toast.success("Watchlist created successfully");
      setIsModalOpen(false);
      watchlistsQuery.refetch();
    } catch (error) {
      onError?.("Failed to create new watchlist");
      toast.error("Failed to create watchlist");
      console.error("Error creating new watchlist:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <div className="add-instrument-container">
      <button
        className={`add-instrument-trigger ${className}`}
        onClick={() => setIsModalOpen(true)}
        title="Add to watchlist"
      >
        <Plus size={16} />
        Add to watchlist
      </button>
      <WatchlistModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        watchlists={watchlistsQuery.data?.data || []}
        instrumentId={instrumentId}
        onAddToWatchlist={handleAddToWatchlist}
        onCreateWatchlist={handleCreateWatchlist}
        isLoading={isLoading}
      />
    </div>
  );
};

export default AddInstrumentWidget;
