import { useCallback, useEffect, useState } from "react";
import "./PromodeModal.scss";

export default function PromodeModal({ isOpen, onClose }) {
  const [activeTab, setActiveTab] = useState("tab1");
  const [selectedInstrument, setSelectedInstrument] = useState(window.EurolandAppContext.command('instrument-selected'));

  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleInstrumentSelected = useCallback(() => {
    setSelectedInstrument(window.EurolandAppContext.command('instrument-selected'));
  }, []);

  useEffect(() => {
    if (window.EurolandAppContext) {
      window.EurolandAppContext.on("instrument-selected", handleInstrumentSelected);
    }
    return () => {
      if (window.EurolandAppContext) {
        window.EurolandAppContext.off("instrument-selected", handleInstrumentSelected);
      }
    };
  }, [handleInstrumentSelected]);


  const handleOverlayKeyDown = (e) => {
    if (e.key === "Escape") {
      onClose();
    }
  };
  console.log("selectedInstrument: ", selectedInstrument);
  return (
    <div
      className="promode-modal-overlay"
      onClick={handleOverlayClick}
      onKeyDown={handleOverlayKeyDown}
      role="button"
      aria-label="Close modal"
      tabIndex={0}
    >
      <div className="promode-modal">
        <div className="promode-modal__header">
          <h2>Promode Modal</h2>
          <button className="promode-modal__close-btn" onClick={onClose}>
            &times;
          </button>
        </div>

        <div className="promode-modal__body">
          <div className="promode-modal__tabs">
            <button
              className={`promode-modal__tab ${
                activeTab === "tab1" ? "active" : ""
              }`}
              onClick={() => setActiveTab("tab1")}
            >
              <i class="fs-market"></i>
            </button>
            <button
              className={`promode-modal__tab ${
                activeTab === "tab2" ? "active" : ""
              }`}
              onClick={() => setActiveTab("tab2")}
            >
              <i class="fs-alert"></i>
            </button>
          </div>

          <div className="promode-modal__content">
            {activeTab === "tab1" && (
              <div className="promode-modal__tab-content">
                <euroland-watch-list />
                <euroland-news-widget instrumentId={selectedInstrument?.id}></euroland-news-widget>

              </div>
            )}
            {activeTab === "tab2" && (
              <div className="promode-modal__tab-content">
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
